#!/usr/bin/env python3
"""
Debug currency detection for the user's specific case.
"""

import sys
import os
sys.path.append('.')

def debug_currency_detection():
    """Debug why JPY is not being detected."""
    
    print("🧪 Debugging Currency Detection Issue")
    print("=" * 50)
    
    try:
        from portfolio_import import PortfolioImportService
        import os
        
        # Test with text that looks like the user's screenshot
        # Based on the screenshot, the values appear to be:
        # SHOP: 1585806,00 (Amount Invested), 75,43 (Buy Price), 21023,90 (Shares)
        # PLTR: 691303,49 (Amount Invested), 25,43 (Buy Price), 27182,55 (Shares)
        # etc.
        
        test_scenarios = [
            {
                'name': 'User Screenshot Format (No ¥ symbols visible)',
                'text': """
                Ticker    Amount Invested    Buy Price    Shares    Purchase Date    Actions
                SHOP      1585806,00         75,43        21023,90  11.04.2025      
                PLTR      691303,49          25,43        27182,55  08.08.2024      
                RBLX      935654,01          36,39        25713,39  08.10.2024      
                PINS      633108,68          30,58        20701,23  02.06.2025      
                SQ        2274568,14         67,47        33714,08  08.11.2024      
                """
            },
            {
                'name': 'With JPY symbols',
                'text': """
                Ticker    Amount Invested    Buy Price    Shares    Purchase Date    Actions
                SHOP      ¥1585806,00        ¥75,43       21023,90  11.04.2025      
                PLTR      ¥691303,49         ¥25,43       27182,55  08.08.2024      
                RBLX      ¥935654,01         ¥36,39       25713,39  08.10.2024      
                PINS      ¥633108,68         ¥30,58       20701,23  02.06.2025      
                SQ        ¥2274568,14        ¥67,47       33714,08  08.11.2024      
                """
            },
            {
                'name': 'Mixed format (JPY values, USD labels)',
                'text': """
                Company                Value (¥)      Change %    Today       Price (USD)
                Shopify Inc.          ¥1,585,806     ▲ 13.71 %   ↑ 0.12 %    USD 75.43
                Palantir Tech.        ¥691,303       ▼ 1.88%     → 0.00 %    $25.43
                Roblox Corp.          ¥935,654       ▲ 9.02%     ↑ 0.31%     36.39 USD
                Pinterest Inc.        ¥633,108       ▲0.96 %     ↑ 0.06%     30.58
                Block Inc.            ¥2,274,568     ▼ 4.20%     ↓ 0.21%     USD: 67.47
                """
            }
        ]
        
        # Get API key from environment
        google_api_key = os.environ.get('GOOGLE_API_KEY', 'test_key')
        service = PortfolioImportService(google_api_key)
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n{i}. Testing: {scenario['name']}")
            print("-" * 40)
            
            # Test currency detection
            detected_currency = service.ai_extractor._detect_primary_currency(scenario['text'])
            
            print(f"   Text sample: {scenario['text'][:100]}...")
            print(f"   Detected currency: {detected_currency}")
            
            if isinstance(detected_currency, dict):
                print(f"   Status: {detected_currency.get('status')}")
                print(f"   Message: {detected_currency.get('message', '')[:100]}...")
                print(f"   Detected currencies: {detected_currency.get('detected_currencies', [])}")
            else:
                if detected_currency == 'JPY':
                    print("   ✅ SUCCESS: JPY correctly detected")
                else:
                    print(f"   ❌ FAILURE: Expected JPY, got {detected_currency}")
        
        # Test 4: Check what currency patterns are being found
        print(f"\n4. Testing Currency Pattern Detection")
        print("-" * 40)
        
        user_text = """
        Ticker    Amount Invested    Buy Price    Shares    Purchase Date    Actions
        SHOP      1585806,00         75,43        21023,90  11.04.2025      
        PLTR      691303,49          25,43        27182,55  08.08.2024      
        """
        
        # Check what patterns are found
        import re
        
        # Check for various currency patterns
        patterns = {
            'JPY_symbols': r'[¥￥]',
            'USD_symbols': r'[\$]',
            'USD_text': r'\bUSD\b',
            'JPY_text': r'\bJPY\b',
            'EUR_symbols': r'[€]',
            'GBP_symbols': r'[£]',
            'comma_decimals': r'\d+,\d{2}\b',  # European decimal format
            'dot_decimals': r'\d+\.\d{2}\b',   # US decimal format
        }
        
        print(f"   Analyzing text: {user_text[:100]}...")
        
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, user_text)
            print(f"   {pattern_name}: {len(matches)} matches - {matches[:5]}")
        
        # Test 5: Check if the issue is in the decimal format
        print(f"\n5. Testing Decimal Format Recognition")
        print("-" * 40)
        
        # European format uses comma as decimal separator
        # This might be a clue that it's a European/Japanese portfolio
        
        european_amounts = re.findall(r'\d+,\d{2}', user_text)
        us_amounts = re.findall(r'\d+\.\d{2}', user_text)
        
        print(f"   European decimal format (comma): {len(european_amounts)} - {european_amounts[:3]}")
        print(f"   US decimal format (dot): {len(us_amounts)} - {us_amounts[:3]}")
        
        if len(european_amounts) > len(us_amounts):
            print("   💡 INSIGHT: European decimal format detected - might indicate non-USD currency")
        else:
            print("   💡 INSIGHT: US decimal format detected - might indicate USD currency")
        
        # Test 6: Manual currency detection with context
        print(f"\n6. Testing Manual Currency Detection Logic")
        print("-" * 40)
        
        # Check if we can infer currency from context
        context_clues = {
            'large_amounts': len(re.findall(r'\b\d{6,}\b', user_text)),  # 6+ digit amounts (common in JPY)
            'small_decimals': len(re.findall(r'\b\d{1,3},\d{2}\b', user_text)),  # Small amounts with decimals
            'medium_amounts': len(re.findall(r'\b\d{4,5},\d{2}\b', user_text)),  # Medium amounts
        }
        
        print(f"   Large amounts (6+ digits): {context_clues['large_amounts']}")
        print(f"   Small amounts (1-3 digits): {context_clues['small_decimals']}")
        print(f"   Medium amounts (4-5 digits): {context_clues['medium_amounts']}")
        
        if context_clues['large_amounts'] > 0:
            print("   💡 INSIGHT: Large amounts detected - could indicate JPY (no decimals in practice)")
        
    except Exception as e:
        print(f"❌ Debug failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎯 Currency Detection Debug Completed!")
    print("\n💡 RECOMMENDATIONS:")
    print("1. If no ¥ symbols are visible, detection relies on amount patterns")
    print("2. Large amounts (6+ digits) often indicate JPY currency")
    print("3. European decimal format (comma) might indicate non-USD currency")
    print("4. Consider adding amount-based currency detection logic")

if __name__ == '__main__':
    debug_currency_detection()
