<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Currency Modal</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #3b82f6; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d1fae5; color: #065f46; }
        .error { background: #fee2e2; color: #991b1b; }
        .info { background: #dbeafe; color: #1e40af; }
    </style>
</head>
<body>
    <h1>Frontend Currency Modal Test</h1>
    <p>This tests if the currency selection modal logic works in the frontend.</p>
    
    <button onclick="testDKKResponse()">Test DKK Response (Should Show Modal)</button>
    <button onclick="testMixedResponse()">Test Mixed Currency Response</button>
    <button onclick="testNoSelectionResponse()">Test No Selection Required</button>
    
    <div id="testResults"></div>

    <script>
        // Simulate the exact API response structure from our backend test
        function testDKKResponse() {
            console.log('Testing DKK response...');
            
            const dkkApiResponse = {
                "success": true,
                "portfolio": [
                    {
                        "ticker": "AAPL",
                        "amount_invested": 161.61,
                        "currency": "DKK"
                    }
                ],
                "detected_currency": "DKK",
                "currency": "DKK",
                "currency_info": {
                    "detected_currencies": ["DKK"],
                    "primary_currency": "DKK",
                    "has_mixed_currencies": false,
                    "requires_user_selection": true,
                    "currency_selection_reason": "Single currency detected",
                    "gemini_question": "I detected DKK currency in your portfolio. Is this correct, or would you prefer to display amounts in a different currency?",
                    "currency_uncertainties": [
                        {
                            "type": "non_standard_currency",
                            "currency": "DKK",
                            "reason": "Detected DKK currency - please confirm this is correct",
                            "confidence": "medium"
                        }
                    ]
                }
            };

            testDisplayResults(dkkApiResponse, 'DKK Response');
        }

        function testMixedResponse() {
            console.log('Testing mixed currency response...');
            
            const mixedApiResponse = {
                "success": true,
                "portfolio": [
                    {"ticker": "AAPL", "currency": "USD"},
                    {"ticker": "ASML", "currency": "EUR"}
                ],
                "detected_currency": "EUR",
                "currency": "EUR",
                "currency_info": {
                    "detected_currencies": ["USD", "EUR"],
                    "primary_currency": "EUR",
                    "has_mixed_currencies": true,
                    "requires_user_selection": true,
                    "gemini_question": "I found multiple currencies in your portfolio (USD, EUR). Which one should I use as the primary display currency?"
                }
            };

            testDisplayResults(mixedApiResponse, 'Mixed Currency Response');
        }

        function testNoSelectionResponse() {
            console.log('Testing no selection required response...');
            
            const noSelectionResponse = {
                "success": true,
                "portfolio": [{"ticker": "AAPL", "currency": "USD"}],
                "detected_currency": "USD",
                "currency": "USD",
                "currency_info": {
                    "detected_currencies": ["USD"],
                    "primary_currency": "USD",
                    "has_mixed_currencies": false,
                    "requires_user_selection": false
                }
            };

            testDisplayResults(noSelectionResponse, 'No Selection Required');
        }

        function testDisplayResults(result, testName) {
            const resultsDiv = document.getElementById('testResults');
            
            // Clear previous results
            resultsDiv.innerHTML = '';

            // Log the test
            console.log(`=== ${testName} Test ===`);
            console.log('API Response:', result);

            // Simulate the displayResults logic from portfolio_import.html
            let testResult = '';
            let shouldShowModal = false;

            try {
                // Check for mixed currencies and show selection modal if needed
                console.log('Checking currency selection requirement:', result.currency_info);
                
                if (result.currency_info && result.currency_info.requires_user_selection) {
                    console.log('Currency selection required! Should show modal...');
                    shouldShowModal = true;
                    testResult = `✅ ${testName}: Modal SHOULD appear`;
                    
                    // Show what the modal would contain
                    const geminiQuestion = result.currency_info.gemini_question;
                    if (geminiQuestion) {
                        testResult += `<br>🤖 Gemini Question: "${geminiQuestion}"`;
                    }
                    
                    const currencies = result.currency_info.detected_currencies || [];
                    testResult += `<br>💱 Currencies: ${currencies.join(', ')}`;
                    
                } else {
                    console.log('No currency selection required, proceeding with results');
                    testResult = `❌ ${testName}: No modal required`;
                }

                // Display result
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${shouldShowModal ? 'success' : 'info'}`;
                resultDiv.innerHTML = testResult;
                resultsDiv.appendChild(resultDiv);

                // If modal should show, simulate showing it
                if (shouldShowModal) {
                    setTimeout(() => {
                        alert(`🎯 MODAL SIMULATION\n\nTest: ${testName}\nGemini Question: ${result.currency_info.gemini_question || 'None'}\nCurrencies: ${result.currency_info.detected_currencies.join(', ')}`);
                    }, 500);
                }

            } catch (error) {
                console.error('Test failed:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'test-result error';
                errorDiv.innerHTML = `❌ ${testName}: JavaScript Error - ${error.message}`;
                resultsDiv.appendChild(errorDiv);
            }
        }
    </script>
</body>
</html>