#!/usr/bin/env python3
"""
Test script to verify the portfolio import fix works correctly.
This tests the fix for the "Processing with AI..." hang issue.
"""

import sys
import os
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set the Google API key for testing
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'

from portfolio_import import process_image_upload

def test_portfolio_import_fix():
    """Test the portfolio import fix with sample data."""
    print("🔧 TESTING PORTFOLIO IMPORT FIX (with timeout protection)")
    print("=" * 60)

    # Create realistic portfolio text that simulates OCR output
    portfolio_text = """
    My Investment Portfolio

    Stock Holdings:
    AAPL    Apple Inc           100 shares    $150.00    $15,000.00
    MSFT    Microsoft Corp       50 shares    $250.00    $12,500.00
    GOOGL   Alphabet Inc         25 shares   $2,000.00   $50,000.00
    TSLA    Tesla Inc            20 shares    $400.00     $8,000.00

    Cash Position: $5,000.00
    Total Portfolio Value: $90,500.00
    """

    # Convert to bytes (this will be recognized as test data)
    image_data = portfolio_text.encode('utf-8')

    print("📤 Testing with sample portfolio data...")
    print(f"   Data size: {len(image_data)} bytes")

    try:
        # Measure processing time to ensure no hanging
        start_time = time.time()
        logger.info("Starting portfolio import test...")

        # Call the main import function
        result = process_image_upload(
            image_data=image_data,
            google_vision_api_key="AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o",
            eodhd_api_key=None
        )

        processing_time = time.time() - start_time
        logger.info(f"Portfolio import completed in {processing_time:.2f} seconds")

        print(f"\n📊 RESULTS:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Processing time: {processing_time:.2f} seconds")
        print(f"   Errors: {len(result.get('errors', []))}")
        print(f"   Warnings: {len(result.get('warnings', []))}")
        print(f"   Portfolio entries: {len(result.get('portfolio', []))}")
        print(f"   Cash position: ${result.get('cash_position', 0):.2f}")

        # Check for timeout issues
        if processing_time > 35:
            print(f"   ⚠️  WARNING: Processing took {processing_time:.2f}s (should be under 35s)")
        elif processing_time > 5:
            print(f"   ℹ️  INFO: Processing took {processing_time:.2f}s (acceptable)")
        else:
            print(f"   ✅ FAST: Processing completed quickly ({processing_time:.2f}s)")
        
        if result.get('errors'):
            print(f"\n❌ ERRORS:")
            for error in result['errors']:
                print(f"   • {error}")
        
        if result.get('warnings'):
            print(f"\n⚠️  WARNINGS:")
            for warning in result['warnings']:
                print(f"   • {warning}")
        
        if result.get('portfolio'):
            print(f"\n📈 PORTFOLIO ENTRIES:")
            for i, entry in enumerate(result['portfolio'], 1):
                ticker = entry.get('ticker', 'UNKNOWN')
                shares = entry.get('shares', 0)
                buy_price = entry.get('buy_price', 0)
                amount_invested = entry.get('amount_invested', 0)
                
                print(f"   {i}. {ticker}")
                print(f"      Shares: {shares}")
                print(f"      Buy Price: ${buy_price:.2f}")
                print(f"      Amount Invested: ${amount_invested:.2f}")
                print()
        
        # Check if the fix worked
        if result.get('success') and len(result.get('portfolio', [])) > 0:
            print("✅ SUCCESS: Portfolio import is working!")
            print("✅ Users should no longer see 'No valid portfolio entries found'")
            return True
        else:
            print("❌ FAILED: Portfolio import still not working")
            print("❌ Users will still see 'No valid portfolio entries found'")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases that might cause issues."""
    print("\n🧪 TESTING EDGE CASES")
    print("=" * 30)
    
    edge_cases = [
        ("Empty text", b""),
        ("Only whitespace", b"   \n\n   "),
        ("No tickers", b"This is just some random text with numbers 123 456"),
        ("Danish format", b"PORTFOLIO_IMAGE_MULTILINGUAL_AAPL_13_161.61_2462.85_DKK"),
        ("Mixed currencies", b"PORTFOLIO_IMAGE_MULTILINGUAL_AAPL_100_150.00_15000.00_USD_MSFT_50_200.00_10000.00_EUR"),
    ]
    
    for test_name, test_text in edge_cases:
        print(f"\n📋 Testing: {test_name}")
        
        try:
            # Use the test data directly if it's already bytes, otherwise encode
            if isinstance(test_text, bytes):
                image_data = test_text
            else:
                image_data = test_text.encode('utf-8')

            result = process_image_upload(
                image_data=image_data,
                google_vision_api_key="AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o",
                eodhd_api_key=None
            )
            
            success = result.get('success', False)
            entries = len(result.get('portfolio', []))
            
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"   Entries: {entries}")
            
            if entries > 0:
                print(f"   First entry: {result['portfolio'][0].get('ticker', 'UNKNOWN')}")
                
        except Exception as e:
            print(f"   Exception: {e}")

if __name__ == "__main__":
    print("🚀 PORTFOLIO IMPORT FIX TEST SUITE")
    print("=" * 60)
    
    # Test main functionality
    main_test_passed = test_portfolio_import_fix()
    
    # Test edge cases
    test_edge_cases()
    
    # Summary
    print(f"\n🎯 SUMMARY")
    print("=" * 20)
    if main_test_passed:
        print("✅ Main functionality: WORKING")
        print("✅ The 'AI extraction failed, falling back to pattern matching' issue should be resolved")
        print("✅ The 'No valid portfolio entries found' error should be fixed")
    else:
        print("❌ Main functionality: STILL BROKEN")
        print("❌ Users will continue to see extraction errors")
    
    print(f"\n📝 NEXT STEPS:")
    print("   1. Test with real image uploads in the web interface")
    print("   2. Verify Gemini AI integration is working")
    print("   3. Test with various portfolio formats and languages")
