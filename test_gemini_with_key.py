#!/usr/bin/env python3
"""
Test Gemini AI portfolio extraction with a real API key
"""

import os
import sys

# Set the Google API key for testing
# Note: In production, this should be set as an environment variable
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'

from portfolio_import import AIPortfolioExtractor

def test_gemini_direct():
    """Test Gemini AI directly with a simple portfolio text"""
    print("🧠 TESTING GEMINI AI DIRECT EXTRACTION")
    print("=" * 60)
    
    # Simple portfolio text
    portfolio_text = """
    Apple Inc (AAPL)
    Shares: 10
    Buy Price: $180.50
    Current Value: $1,950.00
    
    Microsoft Corp (MSFT)
    Shares: 5
    Buy Price: $320.00
    Current Value: $1,750.00
    
    Tesla Inc (TSLA)
    Shares: 3
    Buy Price: $245.00
    Current Value: $735.00
    """
    
    print("Portfolio text:")
    print("-" * 40)
    print(portfolio_text)
    print("-" * 40)
    
    # Create extractor
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    try:
        # Test the Gemini extraction directly
        result = extractor._extract_with_gemini_ai(portfolio_text)
        
        print(f"\n📊 GEMINI DIRECT RESULTS:")
        print(f"Success: {result['success']}")
        
        if result['success']:
            portfolio = result.get('portfolio', [])
            print(f"Entries found: {len(portfolio)}")
            
            for i, entry in enumerate(portfolio, 1):
                print(f"\n{i}. {entry.get('ticker', 'Unknown')}")
                print(f"   Amount Invested: ${entry.get('amount_invested', 0):.2f}")
                print(f"   Buy Price: ${entry.get('buy_price', 0):.2f}")
                print(f"   Shares: {entry.get('shares', 0)}")
                print(f"   Current Value: ${entry.get('current_value', 0):.2f}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
            
        return result['success']
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gemini_full_extraction():
    """Test the full extraction pipeline with Gemini AI"""
    print("\n\n🚀 TESTING FULL GEMINI EXTRACTION PIPELINE")
    print("=" * 60)
    
    # More complex portfolio text
    complex_text = """
    Portfolio Holdings Summary
    
    Technology Stocks:
    Apple Inc. (AAPL)
    - Quantity: 15 shares
    - Average cost: $175.25 per share
    - Market value: $2,850.00
    
    Microsoft Corporation (MSFT)
    - Holdings: 8 shares
    - Cost basis: $310.50 each
    - Current worth: $2,680.00
    
    NVIDIA Corp (NVDA)
    - Position: 4 shares
    - Entry price: $420.75
    - Present value: $1,950.00
    
    Cash Balance: $5,250.00
    """
    
    print("Complex portfolio text:")
    print("-" * 40)
    print(complex_text)
    print("-" * 40)
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    try:
        # Test full extraction
        result = extractor.extract_portfolio_data_with_ai(complex_text)
        
        print(f"\n📊 FULL EXTRACTION RESULTS:")
        print(f"Success: {result['success']}")
        print(f"Method: {result.get('extraction_method', 'unknown')}")
        
        if result['success']:
            portfolio = result.get('portfolio', [])
            print(f"\n💼 PORTFOLIO ENTRIES ({len(portfolio)} found):")
            
            total_invested = 0
            total_current = 0
            
            for i, entry in enumerate(portfolio, 1):
                ticker = entry.get('ticker', 'Unknown')
                invested = entry.get('amount_invested', 0)
                current = entry.get('current_value', 0)
                shares = entry.get('shares', 0)
                buy_price = entry.get('buy_price', 0)
                
                print(f"\n{i}. {ticker}")
                print(f"   Shares: {shares}")
                print(f"   Buy Price: ${buy_price:.2f}")
                print(f"   Amount Invested: ${invested:.2f}")
                print(f"   Current Value: ${current:.2f}")
                
                total_invested += invested
                total_current += current
            
            cash = result.get('cash_position', 0)
            print(f"\n💰 Cash Position: ${cash:.2f}")
            
            print(f"\n📈 SUMMARY:")
            print(f"Total Invested: ${total_invested:.2f}")
            print(f"Total Current Value: ${total_current:.2f}")
            print(f"Total with Cash: ${total_current + cash:.2f}")
            
            if total_invested > 0:
                return_pct = ((total_current - total_invested) / total_invested) * 100
                print(f"Return: {return_pct:.2f}%")
        else:
            print(f"❌ Extraction failed:")
            for error in result.get('errors', []):
                print(f"   - {error}")
                
        return result['success']
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all Gemini tests"""
    print("🧪 TESTING GEMINI AI PORTFOLIO EXTRACTION")
    print("Using Google API Key for Gemini AI")
    print()
    
    # Check if we have the API key
    api_key = os.environ.get('GOOGLE_API_KEY')
    if api_key:
        print(f"✅ API Key found: {api_key[:10]}...")
    else:
        print("❌ No API key found")
        return
    
    # Run tests
    test1_success = test_gemini_direct()
    test2_success = test_gemini_full_extraction()
    
    print("\n" + "=" * 60)
    print("📋 FINAL TEST RESULTS:")
    print(f"✅ Gemini Direct Test: {'PASSED' if test1_success else 'FAILED'}")
    print(f"✅ Full Pipeline Test: {'PASSED' if test2_success else 'FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 ALL GEMINI TESTS PASSED!")
        print("The AI-powered portfolio extraction is working correctly!")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
