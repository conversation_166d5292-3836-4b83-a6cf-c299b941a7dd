#!/usr/bin/env python3
"""
Final test specifically for the DKK ticker issue
"""

from portfolio_import import PortfolioImportService

def test_dkk_issue_resolution():
    """Test that the DKK ticker issue is completely resolved"""
    print("🔍 Final DKK Issue Resolution Test")
    print("=" * 50)
    
    print("User's original problem:")
    print("❌ System was extracting 'DKK' as ticker symbol")
    print("❌ Instead of GOOGL, ASML, UBER, AMZN")
    print()
    
    # Test the exact OCR text that would come from the user's image
    user_ocr_text = """
Ticker Shares Avg. Cost Basis Market Value (DKK) % Chg.

Alphabet Inc.
NasdaqGS:GOOGL 10 161 DKK 12,216.61 18.1%

ASML Holding N.V.
NasdaqGS:ASML 2 668.5 DKK 9,279.65 8.1%

Uber Technologies, Inc.
NYSE:UBER 10 74.59 DKK 5,851.40 22.1%

Amazon.com, Inc.
NasdaqGS:AMZN 8 186.92 DKK 11,790.22 22.7%
"""
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    print("Processing with fixed system...")
    result = service.extract_portfolio_from_text(user_ocr_text)
    
    print(f"\nResults:")
    print(f"Success: {result.success}")
    print(f"Portfolio entries: {len(result.portfolio_entries)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Warnings: {len(result.warnings)}")
    
    # Extract the tickers
    found_tickers = [entry.ticker for entry in result.portfolio_entries]
    print(f"\nFound tickers: {found_tickers}")
    
    # Check for the specific issues
    issues = []
    
    # Issue 1: DKK should NOT be found as a ticker
    if 'DKK' in found_tickers:
        issues.append("❌ DKK still being extracted as ticker")
    else:
        print("✅ DKK correctly filtered out")
    
    # Issue 2: Real tickers should be found
    expected_tickers = ['GOOGL', 'ASML', 'UBER', 'AMZN']
    missing_tickers = [t for t in expected_tickers if t not in found_tickers]
    
    if missing_tickers:
        issues.append(f"❌ Missing expected tickers: {missing_tickers}")
    else:
        print("✅ All expected tickers found")
    
    # Issue 3: No unexpected tickers
    unexpected_tickers = [t for t in found_tickers if t not in expected_tickers]
    if unexpected_tickers:
        issues.append(f"⚠️  Unexpected tickers found: {unexpected_tickers}")
    
    # Show the actual portfolio data
    if result.portfolio_entries:
        print(f"\nExtracted portfolio data:")
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: {entry.shares} shares @ {entry.buy_price} = {entry.amount_invested}")
    
    # Final verdict
    print(f"\n📋 Final Assessment:")
    if issues:
        print("❌ Some issues remain:")
        for issue in issues:
            print(f"  {issue}")
        return False
    else:
        print("🎉 ALL ISSUES RESOLVED!")
        print("✅ DKK no longer extracted as ticker")
        print("✅ Correct tickers (GOOGL, ASML, UBER, AMZN) found")
        print("✅ Exchange-prefixed format handled correctly")
        print("✅ Currency codes properly filtered")
        return True

def test_various_currency_scenarios():
    """Test various currency scenarios to ensure robustness"""
    print(f"\n🔍 Testing Various Currency Scenarios")
    print("=" * 50)
    
    test_cases = [
        {
            'name': 'DKK with real tickers',
            'text': 'GOOGL 10 shares DKK 12,216.61 ASML 2 shares DKK 9,279.65',
            'should_find': ['GOOGL', 'ASML'],
            'should_not_find': ['DKK']
        },
        {
            'name': 'Multiple currencies',
            'text': 'AAPL 5 USD 750 GOOGL 3 DKK 2500 EUR 300',
            'should_find': ['AAPL', 'GOOGL'],
            'should_not_find': ['USD', 'DKK', 'EUR']
        },
        {
            'name': 'Currency-only text',
            'text': 'Total: DKK 50,000 USD 7,500 EUR 6,200',
            'should_find': [],
            'should_not_find': ['DKK', 'USD', 'EUR']
        }
    ]
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    all_passed = True
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        
        result = service.extract_portfolio_from_text(test_case['text'])
        found_tickers = [entry.ticker for entry in result.portfolio_entries]
        
        print(f"  Text: {test_case['text']}")
        print(f"  Found: {found_tickers}")
        
        # Check should_find
        for ticker in test_case['should_find']:
            if ticker in found_tickers:
                print(f"  ✅ Correctly found {ticker}")
            else:
                print(f"  ❌ Missing expected ticker {ticker}")
                all_passed = False
        
        # Check should_not_find
        for ticker in test_case['should_not_find']:
            if ticker not in found_tickers:
                print(f"  ✅ Correctly filtered out {ticker}")
            else:
                print(f"  ❌ Incorrectly found {ticker} as ticker")
                all_passed = False
    
    return all_passed

def main():
    print("🚀 FINAL DKK TICKER ISSUE RESOLUTION TEST")
    print("=" * 60)
    print("Testing that the user's specific DKK ticker issue is resolved")
    print()
    
    test1_passed = test_dkk_issue_resolution()
    test2_passed = test_various_currency_scenarios()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL VERDICT:")
    
    if test1_passed and test2_passed:
        print("🎉 COMPLETE SUCCESS!")
        print()
        print("✅ User's DKK ticker issue is COMPLETELY RESOLVED")
        print("✅ System correctly extracts GOOGL, ASML, UBER, AMZN")
        print("✅ System correctly filters out DKK currency code")
        print("✅ Exchange-prefixed tickers (NasdaqGS:GOOGL) work correctly")
        print("✅ Robust currency filtering across various scenarios")
        print()
        print("🎯 The user can now upload their portfolio image and get:")
        print("   • Correct ticker symbols instead of 'DKK'")
        print("   • Accurate financial data extraction")
        print("   • No more currency codes as stock tickers")
        
    else:
        print("❌ Some issues may still remain.")
        print("   Further investigation needed.")

if __name__ == "__main__":
    main()
