#!/usr/bin/env python3
"""
Test the exact user scenario - simulate their portfolio image
"""

from portfolio_import import process_image_upload, PortfolioImportService
import base64

def test_user_portfolio_scenario():
    """Test with simulated OCR text that matches the user's portfolio image"""
    print("🔍 Testing User's Real Portfolio Scenario")
    print("=" * 60)
    
    # This simulates what OCR should extract from the user's portfolio image
    # Based on the image they showed: GOOGL, ASML, UBER, AMZN with DKK values
    simulated_ocr_text = """
    Ticker    Shares    Avg. Cost Basis    Market Value (DKK)    % Chg.
    
    Alphabet Inc.
    NasdaqGS:GOOGL    10    161    DKK 12,216.61    18.1%
    
    ASML Holding N.V.
    NasdaqGS:ASML    2    668.5    DKK 9,279.65    8.1%
    
    Uber Technologies, Inc.
    NYSE:UBER    10    74.59    DKK 5,851.40    22.1%
    
    Amazon.com, Inc.
    NasdaqGS:AMZN    8    186.92    DKK 11,790.22    22.7%
    """
    
    print("Simulated OCR text from user's portfolio image:")
    print(simulated_ocr_text)
    print("\n" + "=" * 60)
    
    # Test with the portfolio import service directly
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.12345678"
    
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    
    print("Processing with portfolio import service...")
    result = service.extract_portfolio_from_text(simulated_ocr_text)
    
    print(f"\nResults:")
    print(f"Success: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    if result.portfolio_entries:
        print(f"\nExtracted portfolio entries:")
        for i, entry in enumerate(result.portfolio_entries, 1):
            print(f"  {i}. {entry.ticker}")
            print(f"     Shares: {entry.shares}")
            print(f"     Buy Price: ${entry.buy_price:.2f}")
            print(f"     Amount Invested: ${entry.amount_invested:.2f}")
            print(f"     Current Value: ${entry.current_value:.2f}" if entry.current_value is not None else "     Current Value: N/A")
            print(f"     Purchase Date: {entry.purchase_date}")
            print()
    
    # Check if this matches what the user expects
    expected_tickers = ['GOOGL', 'ASML', 'UBER', 'AMZN']
    expected_shares = [10, 2, 10, 8]
    expected_prices = [161.0, 668.5, 74.59, 186.92]
    
    success = True
    if len(result.portfolio_entries) != 4:
        print(f"❌ Expected 4 entries, got {len(result.portfolio_entries)}")
        success = False
    
    for i, entry in enumerate(result.portfolio_entries):
        if i < len(expected_tickers):
            if entry.ticker != expected_tickers[i]:
                print(f"❌ Expected ticker {expected_tickers[i]}, got {entry.ticker}")
                success = False
            if abs(entry.shares - expected_shares[i]) > 0.1:
                print(f"❌ Expected {expected_shares[i]} shares for {entry.ticker}, got {entry.shares}")
                success = False
            if abs(entry.buy_price - expected_prices[i]) > 0.1:
                print(f"❌ Expected ${expected_prices[i]} buy price for {entry.ticker}, got ${entry.buy_price}")
                success = False
    
    if success:
        print("✅ SUCCESS: Portfolio data extracted correctly!")
        print("This is what the user should see when their image is processed correctly.")
    else:
        print("❌ ISSUE: Portfolio data not extracted as expected")
    
    return success

def test_image_processing_with_mock_ocr():
    """Test the full image processing pipeline with mock OCR"""
    print("\n" + "=" * 60)
    print("🔍 Testing Full Image Processing Pipeline")
    print("=" * 60)
    
    # Create a dummy image (this would normally be the user's uploaded image)
    dummy_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.12345678"
    
    print("Processing image with process_image_upload function...")
    result = process_image_upload(dummy_image_data, google_vision_api_key, eodhd_api_key)
    
    print(f"\nResults:")
    print(f"Success: {result['success']}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    print(f"Cash position: ${result.get('cash_position', 0):.2f}")
    print(f"Errors: {len(result.get('errors', []))}")
    print(f"Warnings: {len(result.get('warnings', []))}")
    
    portfolio = result.get('portfolio', [])
    
    if portfolio:
        print(f"\nPortfolio entries found:")
        for entry in portfolio:
            ticker = entry.get('ticker', 'UNKNOWN')
            amount = entry.get('amount_invested', 0)
            price = entry.get('buy_price', 0)
            shares = entry.get('shares', 0)
            
            print(f"  {ticker}: {shares} shares @ ${price:.2f} = ${amount:.2f}")
            
            # Check for the problematic default data the user reported
            if (ticker == 'GOOGL' and abs(amount - 1610.00) < 1.0 and abs(price - 161.00) < 1.0) or \
               (ticker == 'ASML' and abs(amount - 1337.00) < 1.0 and abs(price - 668.50) < 1.0) or \
               (ticker == 'UBER' and abs(amount - 745.90) < 1.0 and abs(price - 74.59) < 1.0) or \
               (ticker == 'AMZN' and abs(amount - 1495.36) < 1.0 and abs(price - 186.92) < 1.0):
                print(f"    ❌ FOUND USER'S REPORTED DEFAULT DATA!")
                return False
    
    if not result['success'] and len(portfolio) == 0:
        print("✅ SUCCESS: System failed gracefully without returning fake data")
        return True
    elif result['success'] and len(portfolio) > 0:
        print("✅ SUCCESS: System processed real data")
        return True
    else:
        print("⚠️  UNKNOWN: Unexpected result")
        return False

if __name__ == "__main__":
    print("Testing User's Real Portfolio Scenario")
    print("=" * 60)
    
    # Test 1: Direct text processing (what should happen if OCR works)
    test1_success = test_user_portfolio_scenario()
    
    # Test 2: Full image processing pipeline (what actually happens)
    test2_success = test_image_processing_with_mock_ocr()
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS:")
    print("=" * 60)
    
    if test1_success and test2_success:
        print("🎉 SUCCESS: Both tests passed!")
        print("✅ Text processing works correctly")
        print("✅ Image processing fails gracefully without fake data")
        print("\n🎯 USER'S ISSUE IS RESOLVED!")
    elif test1_success and not test2_success:
        print("⚠️  PARTIAL SUCCESS:")
        print("✅ Text processing works correctly")
        print("❌ Image processing still returns fake data")
        print("\n🔧 NEED TO FIX: Image processing pipeline")
    else:
        print("❌ TESTS FAILED:")
        print("❌ Text processing has issues")
        print("❌ Image processing has issues")
        print("\n🔧 NEED TO FIX: Core extraction logic")
