#!/usr/bin/env python3
"""
Test script to verify the Gemini AI processing fix works correctly.

This script simulates the exact Gemini AI response that was failing in the user's logs
and tests that the processing now works correctly.
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to the path so we can import portfolio_import
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import AIPortfolioExtractor

def test_gemini_processing_fix():
    """Test the Gemini AI processing fix with the exact data that was failing."""
    
    print("🧪 TESTING GEMINI AI PROCESSING FIX")
    print("=" * 60)
    
    # This simulates the exact Gemini AI response that was failing in the user's logs
    gemini_response_data = [
        {
            'ticker': 'GOOGL',
            'shares': None,
            'buy_price': None,
            'buy_price_currency': None,
            'current_price': 192.06,
            'current_price_currency': 'USD',
            'current_value': 15848.0,
            'current_value_currency': 'DKK',
            'amount_invested': None,
            'company_name': 'Alphabet A',
            'gain_loss_percent': 10.77
        },
        {
            'ticker': 'AMZN',
            'shares': None,
            'buy_price': None,
            'buy_price_currency': None,
            'current_price': 228.29,
            'current_price_currency': 'USD',
            'current_value': 18858.0,
            'current_value_currency': 'DKK',
            'amount_invested': None,
            'company_name': 'Amazon.com',
            'gain_loss_percent': 17.21
        },
        {
            'ticker': 'ASML',
            'shares': None,
            'buy_price': None,
            'buy_price_currency': None,
            'current_price': 718.07,
            'current_price_currency': 'USD',
            'current_value': 31962.0,
            'current_value_currency': 'DKK',
            'amount_invested': None,
            'company_name': 'ASML Holding',
            'gain_loss_percent': -4.27
        },
        {
            'ticker': 'UBER',
            'shares': None,
            'buy_price': None,
            'buy_price_currency': None,
            'current_price': 92.3,
            'current_price_currency': 'USD',
            'current_value': 5886.0,
            'current_value_currency': 'DKK',
            'amount_invested': None,
            'company_name': 'Uber Technologies',
            'gain_loss_percent': 11.14
        }
    ]
    
    print(f"📝 Simulating Gemini AI response with {len(gemini_response_data)} entries")
    print(f"📝 This is the exact data that was failing with '.upper()' error")
    
    try:
        # Initialize the extractor
        extractor = AIPortfolioExtractor("test_key", "test_key")
        extractor.detected_currency = "DKK"  # Set detected currency
        
        # Test the _process_gemini_response method directly
        print(f"\n🔧 Testing _process_gemini_response()...")
        processed_entries = extractor._process_gemini_response(gemini_response_data)
        
        print(f"✅ Gemini processing completed!")
        print(f"   Processed entries: {len(processed_entries)}")
        
        if len(processed_entries) == 0:
            print(f"❌ No entries processed - the fix didn't work")
            return False
        
        # Show the processed entries
        print(f"\n📊 PROCESSED ENTRIES:")
        for i, entry in enumerate(processed_entries, 1):
            print(f"   --- Entry {i}: {entry.get('ticker', 'Unknown')} ---")
            print(f"     Company: {entry.get('company_name', 'N/A')}")
            print(f"     Shares: {entry.get('shares', 'N/A')}")
            print(f"     Buy Price: {entry.get('buy_price', 'N/A')} {entry.get('buy_price_currency', 'N/A')}")
            print(f"     Current Price: {entry.get('current_price', 'N/A')} USD")
            print(f"     Current Value: {entry.get('current_value', 'N/A')} {entry.get('current_value_currency', 'N/A')}")
            print(f"     Amount Invested: {entry.get('amount_invested', 'N/A')} {entry.get('buy_price_currency', 'N/A')}")
            print(f"     Gain/Loss %: {entry.get('gain_loss_percent', 'N/A')}%")
            
            # Validate the calculated shares
            shares = entry.get('shares', 0)
            current_price = entry.get('current_price', 0)
            current_value = entry.get('current_value', 0)
            current_value_currency = entry.get('current_value_currency', 'USD')
            
            if shares and current_price and current_value:
                if current_value_currency == 'DKK':
                    # Convert DKK to USD for validation
                    current_value_usd = current_value * 0.145
                    calculated_shares = current_value_usd / current_price
                    print(f"     ✓ Share calculation: {current_value} DKK → {current_value_usd:.2f} USD ÷ ${current_price} = {calculated_shares:.2f} shares")
                    print(f"     ✓ Actual shares: {shares:.2f}")
                    
                    # Check if calculation is reasonable
                    if abs(shares - calculated_shares) / max(shares, calculated_shares) < 0.1:
                        print(f"     ✅ Share calculation is accurate!")
                    else:
                        print(f"     ⚠️  Share calculation discrepancy")
        
        return len(processed_entries) > 0
        
    except Exception as e:
        print(f"❌ Gemini processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_currency_none_handling():
    """Test that None currency values are handled correctly."""
    
    print(f"\n🧪 TESTING NONE CURRENCY HANDLING")
    print("=" * 60)
    
    # Test data with None currency values (the exact issue from the logs)
    test_data = [
        {
            'ticker': 'TEST',
            'shares': None,
            'buy_price': None,
            'buy_price_currency': None,  # This was causing the .upper() error
            'current_price': 100.0,
            'current_price_currency': 'USD',
            'current_value': 1000.0,
            'current_value_currency': None,  # This was also causing the .upper() error
            'amount_invested': None,
            'company_name': 'Test Company',
            'gain_loss_percent': 5.0
        }
    ]
    
    try:
        extractor = AIPortfolioExtractor("test_key", "test_key")
        extractor.detected_currency = "USD"
        
        print(f"🔧 Testing with None currency values...")
        processed_entries = extractor._process_gemini_response(test_data)
        
        if len(processed_entries) > 0:
            print(f"✅ None currency handling: SUCCESS!")
            entry = processed_entries[0]
            print(f"   Buy Price Currency: {entry.get('buy_price_currency', 'N/A')}")
            print(f"   Current Value Currency: {entry.get('current_value_currency', 'N/A')}")
            return True
        else:
            print(f"❌ None currency handling: FAILED - no entries processed")
            return False
            
    except Exception as e:
        print(f"❌ None currency handling test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 GEMINI AI PROCESSING FIX TEST SUITE")
    print("Testing the exact Gemini AI response that was failing")
    print("=" * 70)
    
    try:
        # Test 1: Gemini processing fix
        gemini_result = test_gemini_processing_fix()
        
        # Test 2: None currency handling
        currency_result = test_currency_none_handling()
        
        print("\n\n✅ GEMINI AI PROCESSING FIX TEST COMPLETED")
        print("=" * 70)
        
        # Summary
        if gemini_result:
            print("🎉 Gemini AI processing fix: SUCCESS!")
            print("✅ The '.upper()' error is fixed!")
            print("✅ Portfolio entries with None currencies are now processed correctly!")
        else:
            print("❌ Gemini AI processing fix: FAILED")
            print("❌ The '.upper()' error still exists")
            
        if currency_result:
            print("✅ None currency handling: SUCCESS!")
        else:
            print("❌ None currency handling: FAILED")
            
        if gemini_result and currency_result:
            print("\n🎯 OVERALL RESULT: SUCCESS!")
            print("✅ Users should no longer see 'No valid portfolio entries found'")
            print("✅ The Gemini AI extraction should work correctly")
            print("✅ Mixed currency portfolios should be processed correctly")
        else:
            print("\n❌ OVERALL RESULT: PARTIAL SUCCESS")
            print("⚠️  Some issues may still exist")
            
    except Exception as e:
        print(f"❌ TEST SUITE FAILED: {e}")
        import traceback
        traceback.print_exc()
