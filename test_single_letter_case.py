#!/usr/bin/env python3
"""
Test the specific single letter 'a' case that was failing
"""

import io
import csv
from portfolio_import import process_spreadsheet_upload

def test_single_letter_a():
    """Test the specific case that was failing: single letter 'a'"""
    print("🔍 Testing Single Letter 'a' Case")
    print("=" * 50)
    
    # Create test CSV with just the problematic case
    output = io.StringIO()
    writer = csv.writer(output)
    
    writer.writerow(['A', 'B', 'C', 'D'])
    writer.writerow(['a', '1500.00', '150.50', '2023-04-15'])
    
    csv_content = output.getvalue().encode('utf-8')
    
    print("Test CSV content:")
    print(output.getvalue())
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_content, 'test_a.csv', google_vision_api_key)
    
    print(f"\nResult:")
    print(f"Success: {result['success']}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Warnings: {result.get('warnings', [])}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    
    portfolio = result.get('portfolio', [])
    if portfolio:
        for entry in portfolio:
            ticker = entry.get('ticker', 'UNKNOWN')
            amount = entry.get('amount_invested', 0)
            price = entry.get('buy_price', 0)
            print(f"  {ticker}: ${amount} @ ${price}")
        
        # Check if we got 'A' as the ticker
        if portfolio[0].get('ticker') == 'A':
            print("✅ SUCCESS: 'a' correctly converted to 'A'")
            return True
        else:
            print(f"❌ FAILED: Expected 'A', got '{portfolio[0].get('ticker')}'")
            return False
    else:
        print("❌ FAILED: No portfolio entries found")
        return False

def test_various_single_letters():
    """Test various single letter cases"""
    print(f"\n🔍 Testing Various Single Letters")
    print("=" * 50)
    
    test_cases = ['a', 'A', 'b', 'B', 'c', 'C']
    
    for letter in test_cases:
        output = io.StringIO()
        writer = csv.writer(output)
        
        writer.writerow(['Ticker', 'Amount', 'Price', 'Date'])
        writer.writerow([letter, '1000.00', '100.00', '2023-04-15'])
        
        csv_content = output.getvalue().encode('utf-8')
        
        google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
        result = process_spreadsheet_upload(csv_content, f'test_{letter}.csv', google_vision_api_key)
        
        portfolio = result.get('portfolio', [])
        if portfolio:
            ticker = portfolio[0].get('ticker', 'UNKNOWN')
            print(f"  '{letter}' -> '{ticker}' ({'✅' if ticker == letter.upper() else '❌'})")
        else:
            errors = result.get('errors', [])
            print(f"  '{letter}' -> ERROR: {errors}")

def main():
    print("🚀 SINGLE LETTER TICKER TEST")
    print("=" * 60)
    print("Testing the specific 'a' case that was causing issues")
    print()
    
    test1 = test_single_letter_a()
    test_various_single_letters()
    
    print("\n" + "=" * 60)
    if test1:
        print("🎉 SUCCESS! Single letter 'a' case is working!")
        print("✅ 'a' correctly converts to ticker 'A'")
        print("✅ No more 'Invalid ticker format: a' errors")
    else:
        print("❌ Single letter 'a' case still has issues")

if __name__ == "__main__":
    main()
