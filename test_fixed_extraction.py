#!/usr/bin/env python3
"""
Test script to verify that the stock extraction fixes work correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService, process_image_upload

def test_correct_stock_extraction():
    """Test that the system extracts the correct stocks from portfolio text."""
    
    print("🧪 Testing Correct Stock Extraction...")
    
    # Create realistic Danish portfolio text with specific stocks
    danish_portfolio_text = """
Min Aktieportefølje

AAPL    13 stk    GAK: 161.61 USD    Markedsværdi: 2.462,85 kr
MSFT    8 stk     GAK: 245.30 USD    Markedsværdi: 1.850,20 kr
GOOGL   5 stk     GAK: 180.45 USD    Markedsværdi: 1.200,15 kr

Total værdi: 5.513,20 kr
Kontanter: 1.250,00 kr
    """.strip()
    
    # Mock the image data with embedded text (simulates successful OCR)
    mock_image_data = f"PORTFOLIO_IMAGE_{danish_portfolio_text}_END".encode('utf-8')
    
    # Process the "image"
    result = process_image_upload(mock_image_data, "test_key", "test_key")
    
    print(f"   Success: {result.get('success', False)}")
    print(f"   Entries found: {len(result.get('portfolio', []))}")
    
    # Check that we got the correct stocks
    portfolio = result.get('portfolio', [])
    extracted_tickers = [entry.get('ticker') for entry in portfolio]
    expected_tickers = ['AAPL', 'MSFT', 'GOOGL']
    
    print(f"   Expected tickers: {expected_tickers}")
    print(f"   Extracted tickers: {extracted_tickers}")
    
    # Check if we got the right tickers (allowing for some extraction variations)
    correct_tickers = []
    wrong_tickers = []
    
    for ticker in extracted_tickers:
        if ticker in expected_tickers:
            correct_tickers.append(ticker)
        else:
            wrong_tickers.append(ticker)
    
    print(f"   Correct tickers: {correct_tickers}")
    print(f"   Wrong tickers: {wrong_tickers}")
    
    # Success criteria: At least 2 of the 3 expected tickers, and no more than 1 wrong ticker
    success = (len(correct_tickers) >= 2 and len(wrong_tickers) <= 1)
    
    if success:
        print("   ✅ SUCCESS: Stock extraction is working correctly!")
        return True
    else:
        print("   ❌ FAILURE: Stock extraction is not accurate enough")
        return False

def test_currency_selection_only_when_confused():
    """Test that currency selection only appears when AI is genuinely confused."""
    
    print("\n🧪 Testing Currency Selection Logic...")
    
    # Test 1: Clear DKK portfolio - should NOT require selection (AI is confident)
    clear_dkk_text = """
Min Aktieportefølje

AAPL    13 stk    GAK: 161.61 USD    Markedsværdi: 2.462,85 kr
MSFT    8 stk     GAK: 245.30 USD    Markedsværdi: 1.850,20 kr
GOOGL   5 stk     GAK: 180.45 USD    Markedsværdi: 1.200,15 kr

Total værdi: 5.513,20 kr
    """.strip()
    
    mock_image_data = f"PORTFOLIO_IMAGE_{clear_dkk_text}_END".encode('utf-8')
    result = process_image_upload(mock_image_data, "test_key", "test_key")
    
    currency_info = result.get('currency_info', {})
    requires_selection = currency_info.get('requires_user_selection', False)
    
    print(f"   Clear DKK portfolio requires selection: {requires_selection}")
    
    # Test 2: Mixed currency with similar usage - should require selection (AI is confused)
    mixed_currency_text = """
International Portfolio

AAPL    10 shares    Cost: $150.00    Value: 1.200,50 kr
MSFT    5 shares     Cost: $250.00    Value: 850,75 kr  
GOOGL   3 shares     Cost: $2000.00   Value: 4.200,00 kr
TSLA    2 shares     Cost: €300.00    Value: €650.00

Total: Mixed currencies
    """.strip()
    
    mock_image_data = f"PORTFOLIO_IMAGE_{mixed_currency_text}_END".encode('utf-8')
    result = process_image_upload(mock_image_data, "test_key", "test_key")
    
    currency_info = result.get('currency_info', {})
    requires_selection_mixed = currency_info.get('requires_user_selection', False)
    
    print(f"   Mixed currency portfolio requires selection: {requires_selection_mixed}")
    
    # Success criteria: Clear portfolio should not require selection, mixed should require selection
    success = (not requires_selection and requires_selection_mixed)
    
    if success:
        print("   ✅ SUCCESS: Currency selection logic is working correctly!")
        return True
    else:
        print("   ❌ FAILURE: Currency selection logic needs adjustment")
        print(f"      Clear DKK should not require selection: {not requires_selection}")
        print(f"      Mixed currencies should require selection: {requires_selection_mixed}")
        return False

def test_us_portfolio_no_selection():
    """Test that clear USD portfolios don't require currency selection."""
    
    print("\n🧪 Testing USD Portfolio (No Selection)...")
    
    us_portfolio_text = """
My Investment Portfolio

AAPL    100 shares    Avg Cost: $150.00    Current Value: $15,000.00
MSFT    50 shares     Avg Cost: $250.00    Current Value: $12,500.00
GOOGL   25 shares     Avg Cost: $2,000.00  Current Value: $50,000.00

Total Value: $77,500.00
Cash: $5,000.00
    """.strip()
    
    mock_image_data = f"PORTFOLIO_IMAGE_{us_portfolio_text}_END".encode('utf-8')
    result = process_image_upload(mock_image_data, "test_key", "test_key")
    
    currency_info = result.get('currency_info', {})
    requires_selection = currency_info.get('requires_user_selection', False)
    primary_currency = currency_info.get('primary_currency', 'Unknown')
    
    print(f"   Primary currency: {primary_currency}")
    print(f"   Requires selection: {requires_selection}")
    
    if not requires_selection and primary_currency == 'USD':
        print("   ✅ SUCCESS: USD portfolio correctly doesn't require selection!")
        return True
    else:
        print("   ❌ FAILURE: USD portfolio should not require currency selection")
        return False

def run_all_tests():
    """Run all tests and provide summary."""
    
    print("🚀 Testing Fixed Stock Extraction and Currency Logic")
    print("=" * 60)
    
    tests = [
        ("Correct Stock Extraction", test_correct_stock_extraction),
        ("Currency Selection Logic", test_currency_selection_only_when_confused),
        ("USD No Selection", test_us_portfolio_no_selection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   💥 ERROR: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The fixes are working correctly.")
        return True
    else:
        print(f"\n💥 {total - passed} test(s) failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)