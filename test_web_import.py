#!/usr/bin/env python3
"""
Test script to verify the portfolio import fix works in the web application.
This simulates the actual web request flow.
"""

import requests
import json
import time
import io

def test_web_portfolio_import():
    """Test the portfolio import through the web API."""
    print("🌐 TESTING WEB PORTFOLIO IMPORT")
    print("=" * 50)
    
    # Create test portfolio text
    portfolio_text = """
    My Investment Portfolio
    
    Stock Holdings:
    AAPL    Apple Inc           100 shares    $150.00    $15,000.00
    MSFT    Microsoft Corp       50 shares    $250.00    $12,500.00
    GOOGL   Alphabet Inc         25 shares   $2,000.00   $50,000.00
    TSLA    Tesla Inc            20 shares    $400.00     $8,000.00
    
    Cash Position: $5,000.00
    Total Portfolio Value: $90,500.00
    """
    
    # Convert to bytes for image upload
    image_data = portfolio_text.encode('utf-8')
    
    # Create a file-like object
    files = {
        'image': ('portfolio.txt', io.BytesIO(image_data), 'image/png')
    }
    
    print(f"📤 Uploading test portfolio data ({len(image_data)} bytes)...")
    
    try:
        # Start timing
        start_time = time.time()
        
        # Make request to the web API
        response = requests.post(
            'http://localhost:5000/api/import/image',
            files=files,
            timeout=60  # 60 second timeout for the web request
        )
        
        processing_time = time.time() - start_time
        
        print(f"⏱️  Request completed in {processing_time:.2f} seconds")
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"\n📈 RESULTS:")
            print(f"   Success: {result.get('success', False)}")
            print(f"   Errors: {len(result.get('errors', []))}")
            print(f"   Warnings: {len(result.get('warnings', []))}")
            print(f"   Portfolio entries: {len(result.get('portfolio', []))}")
            print(f"   Cash position: ${result.get('cash_position', 0):.2f}")
            
            if result.get('errors'):
                print(f"\n❌ ERRORS:")
                for error in result['errors']:
                    print(f"   • {error}")
            
            if result.get('warnings'):
                print(f"\n⚠️  WARNINGS:")
                for warning in result['warnings']:
                    print(f"   • {warning}")
            
            if result.get('portfolio'):
                print(f"\n📊 PORTFOLIO ENTRIES:")
                for i, entry in enumerate(result['portfolio'], 1):
                    ticker = entry.get('ticker', 'UNKNOWN')
                    shares = entry.get('shares', 0)
                    buy_price = entry.get('buy_price', 0)
                    amount_invested = entry.get('amount_invested', 0)
                    
                    print(f"   {i}. {ticker}")
                    print(f"      Shares: {shares}")
                    print(f"      Buy Price: ${buy_price:.2f}")
                    print(f"      Amount Invested: ${amount_invested:.2f}")
            
            # Check if the fix worked
            if result.get('success') and len(result.get('portfolio', [])) > 0:
                print(f"\n✅ SUCCESS: Web portfolio import is working!")
                print(f"✅ Processing time: {processing_time:.2f} seconds (should be under 60s)")
                if processing_time > 35:
                    print(f"⚠️  WARNING: Took longer than expected ({processing_time:.2f}s)")
                return True
            else:
                print(f"\n❌ FAILED: Web portfolio import not working")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"❌ TIMEOUT: Request took longer than 60 seconds")
        print(f"❌ This indicates the 'Processing with AI...' hang issue is NOT fixed")
        return False
    except requests.exceptions.ConnectionError:
        print(f"❌ CONNECTION ERROR: Could not connect to http://localhost:5000")
        print(f"ℹ️  Make sure the Flask app is running with: python app.py")
        return False
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def test_with_skip_gemini():
    """Test with Gemini AI disabled to ensure fast processing."""
    print(f"\n🚫 TESTING WITH GEMINI AI DISABLED")
    print("=" * 40)
    
    # Set environment variable to skip Gemini AI
    import os
    os.environ['SKIP_GEMINI_AI'] = 'true'
    
    try:
        # Import and test directly
        from portfolio_import import process_image_upload
        
        portfolio_text = """
        My Investment Portfolio
        AAPL 100 shares $150.00 $15,000.00
        MSFT 50 shares $250.00 $12,500.00
        Cash: $5,000.00
        """
        
        image_data = portfolio_text.encode('utf-8')
        
        start_time = time.time()
        
        result = process_image_upload(
            image_data=image_data,
            google_vision_api_key="test_key",
            eodhd_api_key=None
        )
        
        processing_time = time.time() - start_time
        
        print(f"⏱️  Direct processing time: {processing_time:.2f} seconds")
        print(f"📊 Success: {result.get('success', False)}")
        print(f"📊 Entries: {len(result.get('portfolio', []))}")
        
        if processing_time < 5 and result.get('success'):
            print(f"✅ FAST PROCESSING: Gemini bypass works correctly")
            return True
        else:
            print(f"❌ SLOW PROCESSING: Issue may still exist")
            return False
            
    finally:
        # Clean up environment variable
        if 'SKIP_GEMINI_AI' in os.environ:
            del os.environ['SKIP_GEMINI_AI']

def main():
    """Run all web tests."""
    print("🚀 WEB PORTFOLIO IMPORT TEST SUITE")
    print("=" * 60)
    
    # Test 1: Direct processing with Gemini disabled (should be fast)
    test1_success = test_with_skip_gemini()
    
    # Test 2: Web API with timeout protection
    test2_success = test_web_portfolio_import()
    
    # Summary
    print(f"\n🎯 SUMMARY")
    print("=" * 20)
    print(f"Test 1 (Direct with Gemini disabled): {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"Test 2 (Web API with timeout): {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ The 'Processing with AI...' hang issue is FIXED")
        print(f"✅ Users should no longer experience infinite loading")
        print(f"✅ Fallback mechanisms work when Gemini AI times out")
    elif test1_success:
        print(f"\n⚠️  PARTIAL SUCCESS")
        print(f"✅ Direct processing works (Gemini bypass)")
        print(f"❌ Web API may still have issues")
        print(f"ℹ️  Make sure Flask app is running for web tests")
    else:
        print(f"\n❌ TESTS FAILED")
        print(f"❌ The hang issue may not be fully resolved")
    
    return 0 if (test1_success and test2_success) else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
