#!/usr/bin/env python3
"""
Test OCR.space API directly
"""

import requests
import json

def test_ocr_space_api():
    """Test OCR.space API with a simple image"""
    
    # Create a simple test image (1x1 PNG)
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    
    try:
        print("Testing OCR.space API...")
        
        # OCR.space free API endpoint
        url = "https://api.ocr.space/parse/image"
        
        # Prepare the request with better settings for table data
        files = {
            'file': ('image.png', test_image_data, 'image/png')
        }
        
        data = {
            'apikey': 'helloworld',  # Free API key
            'language': 'eng',
            'isOverlayRequired': 'false',
            'detectOrientation': 'true',
            'scale': 'true',
            'OCREngine': '2',  # Use engine 2 for better accuracy
            'isTable': 'true'  # Enable table detection
        }
        
        print("Sending request to OCR.space API...")
        response = requests.post(url, files=files, data=data, timeout=30)
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API Response:")
            print(json.dumps(result, indent=2))
            
            if result.get('IsErroredOnProcessing', True):
                error_msg = result.get('ErrorMessage', 'Unknown OCR error')
                print(f"❌ OCR.space error: {error_msg}")
            else:
                # Extract text from results
                parsed_results = result.get('ParsedResults', [])
                if parsed_results:
                    extracted_text = parsed_results[0].get('ParsedText', '')
                    print(f"✅ Successfully extracted text: '{extracted_text}'")
                else:
                    print("✅ API working but no text found in test image (expected)")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response text: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ocr_space_api()
