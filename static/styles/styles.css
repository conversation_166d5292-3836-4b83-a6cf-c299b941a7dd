/* General Body Styling */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--bg-color);
    color: var(--text-color); /* Use theme variable */
    line-height: 1.6;
    transition:
        background-color 0.3s ease,
        color 0.3s ease; /* Smooth theme transitions */
}

/* Header Styling - Assuming base.html handles header now, remove if not needed */
/* If header styles are still needed here, update them to use CSS variables */

/* Main Content Styling */
main {
    padding: 25px; /* Slightly more padding */
    margin: 0 auto;
    max-width: 1300px; /* Slightly wider max-width */
}

h2 {
    text-align: center;
    margin-bottom: 30px; /* More space below h2 */
    color: var(--text-color); /* Use theme variable */
    font-size: 2rem; /* Larger heading */
    font-weight: 600;
}

/* Result Box Styling */
.result-box {
    background-color: var(
        --card-bg-color,
        white
    ); /* Use theme variable or fallback */
    padding: 25px; /* More padding */
    border-radius: 12px; /* Slightly more rounded */
    box-shadow: var(
        --shadow-md,
        0 4px 12px rgba(0, 0, 0, 0.08)
    ); /* Use theme variable or fallback */
    margin-bottom: 35px; /* More space below */
    text-align: center;
    border: 1px solid var(--border-color, #ddd); /* Use theme variable or fallback */
    transition:
        transform 0.3s ease,
        box-shadow 0.3s ease; /* Add hover transition */
}

.result-box:hover {
    transform: translateY(-3px); /* Slight lift on hover */
    box-shadow: var(
        --shadow-lg,
        0 8px 20px rgba(0, 0, 0, 0.12)
    ); /* Enhanced shadow on hover */
}

.result-box p {
    font-size: 1.2em;
    margin: 10px 0;
}

/* Chart Box Styling */
.chart-box {
    background-color: var(--card-bg-color, white);
    padding: 25px;
    border-radius: 12px;
    box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.08));
    margin-bottom: 35px;
    text-align: center;
    border: 1px solid var(--border-color, #ddd); /* Consistent border */
    transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;
}

.chart-box:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg, 0 8px 20px rgba(0, 0, 0, 0.12));
}

.chart-box p {
    font-size: 1.2em;
    margin: 10px 0;
    color: var(--negative-color);
}

.chart-container {
    text-align: center;
    position: relative;
    width: 80%;
    margin: auto;
    display: block; /* Ensure the chart container is visible */
}

.chart {
    width: 100%;
    height: 300px; /* Adjusted height */
    display: block; /* Ensure the chart is visible */
}

.chart canvas {
    width: 100% !important;
    height: 300px !important; /* Adjusted height */
    display: block; /* Ensure the canvas is visible */
}

.chartjs-tooltip {
    opacity: 1;
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: var(--text-color);
    border-radius: 3px;
    transition: all 0.1s ease;
    pointer-events: none;
    transform: translate(-50%, 0);
}

.chartjs-tooltip-key {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 10px;
}

.chartjs-tooltip-value {
    display: inline-block;
    margin-left: 10px;
}

/* Form Styling */
form {
    max-width: 600px; /* Slightly wider forms */
    margin: 40px auto; /* More vertical margin */
    background-color: var(--card-bg-color, white);
    padding: 35px; /* More padding */
    border-radius: 12px;
    box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.08));
    margin-bottom: 40px; /* More space below */
    border: 1px solid var(--border-color, #ddd);
}

form label {
    font-weight: 600; /* Slightly bolder */
    color: var(--text-color);
    margin-top: 15px; /* More space above label */
    margin-bottom: 5px; /* Space between label and input */
    display: block;
    font-size: 0.95rem;
}

form input,
form select,
form button {
    width: 100%;
    padding: 12px 15px; /* More padding */
    margin-top: 5px; /* Reduced top margin as label has bottom margin */
    border: 1px solid var(--border-color, #ccc); /* Use theme variable */
    border-radius: 8px; /* More rounded */
    font-size: 1rem;
    background-color: var(--input-bg-color, white); /* Use theme variable */
    color: var(--input-text-color, #333); /* Use theme variable */
    box-sizing: border-box; /* Include padding and border in element's total width/height */
    transition:
        border-color 0.3s ease,
        box-shadow 0.3s ease;
}

form input:focus,
form select:focus {
    outline: none;
    border-color: var(--highlight-color, #007bff);
    box-shadow: 0 0 0 3px rgba(var(--highlight-color-rgb, 0, 123, 255), 0.15); /* Focus ring */
}

form button {
    background-color: var(--button-bg-color, #007bff);
    color: var(--button-text-color, white);
    cursor: pointer;
    font-size: 1.1em;
    font-weight: 600;
    margin-top: 25px; /* More space above button */
    transition:
        background-color 0.3s ease,
        transform 0.1s ease;
    border: none; /* Ensure no default border */
}

form button:hover {
    background-color: var(
        --button-hover-bg-color,
        var(--highlight-darker, #0056b3)
    ); /* Use theme variable or fallback */
    opacity: 0.95; /* Slight opacity change */
}

form button:active {
    transform: scale(0.98); /* Click feedback */
}

/* Table Styling */
table {
    width: 100%;
    margin-top: 35px; /* More space above table */
    border-collapse: collapse; /* Keep collapsed */
    background-color: var(--card-bg-color, white);
    border-radius: 12px; /* Match card radius */
    box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.08));
    overflow: hidden; /* Ensures radius applies to content */
    border: 1px solid var(--table-border-color, var(--border-color, #ddd)); /* Use specific or fallback */
}

table th,
table td {
    padding: 15px; /* More padding */
    text-align: left; /* Align text left for readability */
    border-bottom: 1px solid
        var(--table-border-color, var(--border-color, #ddd)); /* Horizontal lines */
    vertical-align: middle; /* Align content vertically */
}

table th {
    background-color: var(
        --table-header-bg,
        #f8f9fa
    ); /* Use theme variable or fallback */
    font-weight: 600; /* Bolder headers */
    color: var(--text-color);
    font-size: 0.9rem; /* Slightly smaller header font */
    text-transform: uppercase; /* Uppercase headers */
    letter-spacing: 0.5px;
    border-bottom-width: 2px; /* Thicker line below header */
}

table td {
    font-size: 1rem;
    color: var(--text-color);
}

/* Add hover effect to table rows */
table tbody tr {
    transition: background-color 0.2s ease;
}

table tbody tr:hover {
    background-color: var(
        --nav-hover-color,
        #f1f1f1
    ); /* Use theme variable or fallback */
}

/* Remove border from last row */
table tbody tr:last-child td {
    border-bottom: none;
}

table a {
    color: var(--highlight-color, #007bff); /* Use theme highlight color */
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

table a:hover {
    text-decoration: underline;
    color: var(--highlight-darker, #0056b3); /* Darker highlight on hover */
}

/* Style action buttons in table */
table .edit-btn,
table .delete-btn {
    padding: 6px 12px; /* Adjust padding */
    font-size: 0.9rem;
    margin: 0 3px; /* Add small margin between buttons */
    border-radius: 6px;
}

.icon-delete,
.icon-update {
    /* Assuming these are img or svg */
    width: 18px; /* Slightly larger */
    height: 18px;
    vertical-align: middle; /* Align icons better */
}

/* Error and Message Handling */
.error {
    color: var(--negative-color, red); /* Use theme variable or fallback */
    font-weight: 600;
    text-align: center;
    margin-top: 25px;
    padding: 15px;
    background-color: rgba(
        var(--negative-color-rgb, 255, 0, 0),
        0.1
    ); /* Light background */
    border: 1px solid rgba(var(--negative-color-rgb, 255, 0, 0), 0.3);
    border-radius: 8px;
}

.success {
    color: var(--positive-color, green); /* Use theme variable or fallback */
    font-weight: 600;
    text-align: center;
    margin-top: 25px;
    padding: 15px;
    background-color: rgba(
        var(--positive-color-rgb, 0, 128, 0),
        0.1
    ); /* Light background */
    border: 1px solid rgba(var(--positive-color-rgb, 0, 128, 0), 0.3);
    border-radius: 8px;
}

/* Footer Styling */
footer {
    background-color: var(
        --footer-bg-color,
        #343a40
    ); /* Use theme variable or fallback */
    color: var(--button-text-color, white);
    padding: 15px 0; /* More padding */
    text-align: center;
    font-size: 0.9em;
    margin-top: 50px; /* More space above footer */
    /* Remove position: fixed unless absolutely necessary */
    /* position: fixed; */
    /* width: 100%; */
    /* bottom: 0; */
}

footer a {
    color: var(--button-text-color, white);
    text-decoration: none;
    transition: color 0.2s ease;
}

footer a:hover {
    text-decoration: underline;
    opacity: 0.8; /* Slight fade on hover */
}

/* Responsive Design */
@media (max-width: 768px) {
    header nav {
        text-align: center;
    }

    header nav a {
        margin: 10px 15px;
        font-size: 1em;
    }

    form {
        padding: 20px;
        margin: 10px;
    }

    table th,
    table td {
        font-size: 0.9em;
        padding: 8px;
    }

    h2 {
        font-size: 1.6em;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.5rem;
    }

    form {
        padding: 15px;
    }

    table {
        font-size: 0.9em;
    }

    table th,
    table td {
        padding: 6px;
    }
}

/* Custom Classes for Today's Change (Use theme variables) */
.red {
    color: var(--negative-color, red);
    font-weight: 500;
}

.green {
    color: var(--positive-color, green);
    font-weight: 500;
}

.black {
    color: var(--text-color, black); /* Use theme text color */
    font-weight: 500;
}

canvas {
    display: block;
    margin: 0 auto;
}

.chartjs-tooltip {
    opacity: 1;
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 3px;
    transition: all 0.1s ease;
    pointer-events: none;
    transform: translate(-50%, 0);
}

.chartjs-tooltip-key {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 10px;
}

.chartjs-tooltip-value {
    display: inline-block;
    margin-left: 10px;
}

#toggleLineChart {
    margin-bottom: 10px;
}

#loading {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5em;
    color: #333;
}

#chart {
    width: 100%;
    height: 500px;
}

/* =============================================== */
/* === D3.js SVG Score Gauge Styles === */
/* =============================================== */

.score-gauge-chart-container {
    display: flex;
    justify-content: center; /* Centers the SVG chart horizontally within this div */
    align-items: center;
    margin-top: 12px; /* Space between main text and chart */
    margin-bottom: 8px; /* Space between chart and risk text (if any) */
    width: 100%; /* Takes the width of the parent <p> bubble */
    min-height: 80px; /* Minimum height for the chart SVG */
    /* border: 1px dashed blue; /* TEMPORARY: For visualizing the container */
}

.score-gauge-svg {
    display: block;
    overflow: visible;
}

.gauge-background {
    opacity: 0.7;
}

.gauge-foreground {
    transition:
        stroke-dashoffset 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        stroke 0.5s ease-in-out;
}

.gauge-center-text {
    /* The <text> element in SVG */
    font-family: var(
        --bs-body-font-family,
        system-ui,
        sans-serif
    ); /* Use theme font */
    /* fill is set dynamically in JS */
}

.gauge-score {
    /* The <tspan> for the number */
    font-size: 18px; /* Adjust as needed */
    font-weight: 600; /* Adjust as needed */
    /* fill is set dynamically in JS */
}

.gauge-percent-sign {
    /* The <tspan> for the '%' */
    font-size: 10px; /* Adjust as needed */
    /* fill is set dynamically in JS */
    /* dx, dy for positioning are set in JS if needed, or here */
}

.score-gauge-risk-text {
    text-align: center;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0px; /* Adjust if chart container has enough bottom margin */
    margin-bottom: 5px;
    padding: 0 5px;
    line-height: 1.3;
    width: 100%;
    /* color is set dynamically in JS */
}
/* === AGGRESSIVE DEBUG STYLES FOR CHATBOT CHART === */

/* Ensure the message bubble itself is flexible and visible */
.message.bot p {
    /* Target the <p> tag that is the message bubble */
    display: flex; /* Helps in structuring content within the bubble */
    flex-direction: column; /* Stack text, chart, risk text vertically */
    align-items: flex-start; /* Align content to the start by default */
    min-height: 60px !important;
    overflow: visible !important; /* Crucial */
    /* border: 1px dotted hotpink !important; */ /* DEBUG: border for the <p> bubble */
}

/* Force the chart's container div (score-gauge-chart-container) to be visible and sized */
.message.bot p .score-gauge-chart-container {
    display: flex !important;
    justify-content: center !important; /* Center iframe horizontally */
    align-items: center !important; /* Center iframe vertically */
    width: 100% !important; /* Takes width of parent <p> */
    min-height: 85px !important; /* Min height for the container (80px chart + padding/margin) */
    padding: 5px 0 !important; /* Some vertical padding if needed */
    margin: 8px 0 !important; /* Vertical margin within the bubble */
    /* background-color: rgba(0, 255, 0, 0.1) !important; */ /* DEBUG: Light green for container */
    /* border: 1px dashed darkgreen !important; */ /* DEBUG: border for container */
    box-sizing: border-box !important;
    position: relative !important;
    z-index: 100 !important;
    overflow: visible !important; /* Ensure children (iframe) are not clipped by this container */
}

/* Force the iframe to be visible and sized */
.message.bot p .score-gauge-chart-container iframe {
    display: block !important;
    width: 80px !important; /* Fixed width for the iframe */
    height: 80px !important; /* Fixed height for the iframe */
    border: none !important; /* Remove iframe default border */
    /* background-color: rgba(255, 100, 100, 0.3) !important; */ /* DEBUG: Light red for iframe itself */
    visibility: visible !important;
    opacity: 1 !important;
    overflow: hidden !important; /* iframe's own overflow for ITS content */
    position: relative !important; /* Or static */
    z-index: 101 !important; /* Above its container's background */
    /* margin: 0 auto !important; /* Centered by flex container now */
}

/* Ensure risk text below is also visible and styled */
.message.bot p .score-gauge-risk-text {
    display: block !important;
    width: 100% !important; /* Take full width for centering */
    text-align: center !important; /* Center the risk text */
    margin-top: 4px !important; /* Space above risk text */
    /* border: 1px dotted mediumpurple !important; */ /* DEBUG */
    color: var(--chatbot-text-color) !important;
    font-size: 0.75rem !important;
    position: relative !important;
    z-index: 100 !important;
}

/* Dark Theme Styles - Inherit from base.html theme variables */
[data-theme="dark"] {
    /* Use theme variables from base.html for consistency */
    --video-bg-color: var(--card-bg-color);
    --chart-bg-color: var(--bg-color);
    --chart-grid-color: var(--border-color);
    --current-price-color: var(--text-color);
    --bottom-box-bg-color: var(--card-bg-color);
    --result-box-border-color: var(--text-color);
    --footer-bg-color: var(--highlight-color);
    --result-box-bg-color: var(--card-bg-color);
    --table-border-color: var(--border-color);
    --arrow-stroke-color: #000000; /* Black stroke for dark mode */
    --icon-color-invert: 1; /* Invert icons to white */
}

/* Icon color adaptation system */
.icon-adaptive {
    color: var(--icon-color);
    transition: color 0.3s ease;
}

/* Icons on dark backgrounds should be white */
[data-theme="dark"] .icon-adaptive,
[data-theme="dark"] i,
[data-theme="dark"] .fas,
[data-theme="dark"] .fab,
[data-theme="dark"] .far {
    color: var(--text-color);
}

/* Icons on light backgrounds should be dark */
:root .icon-adaptive,
:root i,
:root .fas,
:root .fab,
:root .far {
    color: var(--icon-color);
}

/* Specific icon color overrides for branded elements */
.fab.fa-google {
    color: #4285f4 !important;
}

.fab.fa-google[data-theme="dark"] {
    color: #4285f4 !important;
}

/* Search icons */
.search-icon {
    color: var(--text-muted-color);
    transition: color 0.3s ease;
}

[data-theme="dark"] .search-icon {
    color: var(--text-muted-color);
}

/* Ensure global text color applies */
[data-theme="dark"] body,
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6,
[data-theme="dark"] p,
[data-theme="dark"] label {
    color: var(--text-color); /* Apply white text globally */
}

/* Inputs and textareas */
[data-theme="dark"] input,
[data-theme="dark"] textarea {
    background-color: var(--input-bg-color);
    color: var(--input-text-color);
}

/* Buttons */
[data-theme="dark"] button {
    background-color: var(--button-bg-color);
    color: var(--button-text-color);
}

/* Light Theme Styles - NOTE: Main theme variables are defined in base.html */
:root {
    /* Additional variables specific to this stylesheet that extend base.html */
    --button-hover-bg-color: var(--highlight-darker, #0056b3);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
    --positive-color-rgb: 25, 135, 84;
    --negative-color-rgb: 220, 53, 69;
    --icon-color-invert: 0; /* Keep icons as they are */
    --arrow-color: #000000;
    --arrow-stroke-color: #ffffff;
}

/* Light mode specific fixes to prevent black backgrounds */
:root body {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
}

:root .card,
:root .chart-box,
:root .result-box {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
}

:root .portfolio-item,
:root .analysis-content,
:root .investment-analysis-content {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
}

/* Ensure all containers use light theme colors */
:root .container,
:root .main-content,
:root .page-container {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
}

/* Fix any remaining black elements in light mode */
:root * {
    border-color: var(--border-color) !important;
}

:root .modal-content,
:root .dropdown-menu,
:root .tooltip {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

    /* Additional chatbot-specific variables that extend base.html */
    --chatbot-padding: 10px 15px;
    --chatbot-border-radius: 18px;
    --chatbot-message-shadow: var(--shadow-sm);
    --chatbot-quick-reply-bg: #f1f1f1;
    --chatbot-quick-reply-text: var(--highlight-color);
    --chatbot-quick-reply-hover-bg: #e0e0e0;
    --chatbot-code-block-bg: #2b2b3d; /* Dark background for code blocks */
    --chatbot-code-block-text: #f8f8f2; /* Light text for code blocks */
    --chatbot-code-block-border: #444466;
    --chatbot-link-color: var(--highlight-color);
    --chatbot-info-text-color: #17a2b8; /* Info blue */
    --chatbot-info-bg-color: rgba(23, 162, 184, 0.1);
    --chatbot-warning-text-color: #ffc107; /* Warning yellow */
    --chatbot-warning-bg-color: rgba(255, 193, 7, 0.1);
}

/* Dark Theme Chatbot Variables - Additional variables that extend base.html */
[data-theme="dark"] {
    --chatbot-quick-reply-bg: var(--nav-hover-color);
    --chatbot-quick-reply-text: var(--highlight-color);
    --chatbot-quick-reply-hover-bg: var(--input-bg-color);
    --chatbot-code-block-bg: #171723; /* Very dark for code blocks */
    --chatbot-code-block-text: #d1d1d1; /* Light grey for code text */
    --chatbot-code-block-border: var(--border-color);
    --chatbot-link-color: var(--highlight-color);
    --chatbot-error-text-color: #ff8080; /* Lighter red for dark mode */
    --chatbot-error-bg-color: rgba(255, 128, 128, 0.1);
    --chatbot-success-text-color: #80ff80; /* Lighter green */
    --chatbot-success-bg-color: rgba(128, 255, 128, 0.1);
    --chatbot-info-text-color: #80d4ff; /* Lighter info blue */
    --chatbot-info-bg-color: rgba(128, 212, 255, 0.1);
    --chatbot-warning-text-color: #ffd780; /* Lighter warning yellow */
    --chatbot-warning-bg-color: rgba(255, 215, 128, 0.1);
}

/* Chatbot Container */
.chatbot-container {
    display: flex;
    flex-direction: column;
    height: 70vh; /* Adjust as needed, or make it dynamic */
    max-height: 600px; /* Max height */
    width: 100%;
    max-width: 450px; /* Max width for a typical chatbot window */
    margin: 20px auto; /* Center it */
    background-color: var(--chatbot-bg);
    border: 1px solid var(--chatbot-border-color);
    border-radius: 12px; /* More rounded corners */
    box-shadow: var(--shadow-lg); /* Prominent shadow */
    overflow: hidden; /* Important for keeping children within rounded corners */
    position: relative; /* For absolute positioning of elements like settings button */
}

/* Chatbot Header */
.chatbot-header {
    background: var(--chatbot-header-bg);
    color: var(
        --chatbot-text
    ); /* Use main text color, or a specific header text color */
    padding: 15px 20px; /* More padding */
    text-align: center;
    font-size: 1.25rem; /* Larger font */
    font-weight: 600;
    border-bottom: 1px solid var(--chatbot-border-color);
    display: flex;
    justify-content: space-between; /* Align title and settings icon */
    align-items: center;
    position: relative; /* For z-index if needed */
}

.chatbot-header h2 {
    margin: 0;
    font-size: 1.1rem; /* Adjusted to fit better */
    color: #ffffff; /* Explicitly white for header */
}

/* Settings Icon in Header */
.chatbot-settings-icon {
    background: none;
    border: none;
    color: var(--chatbot-settings-icon-color); /* Use variable */
    font-size: 1.5rem; /* Adjust size */
    cursor: pointer;
    padding: 5px;
    line-height: 1; /* Ensure icon is centered */
}

.chatbot-settings-icon:hover {
    opacity: 0.8;
}

/* Message Area */
.messages-area {
    flex-grow: 1;
    padding: 15px; /* Consistent padding */
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px; /* Space between messages */
    background-color: var(--chatbot-bg); /* Ensure it matches container */
}

/* Individual Message Styling */
.message {
    display: flex;
    align-items: flex-end; /* Align avatar with bottom of message bubble */
    max-width: 85%; /* Max width of a message bubble */
    opacity: 0; /* Start hidden for animation */
    transform: translateY(10px); /* Start slightly lower for animation */
    animation: message-fade-in 0.4s ease forwards;
    animation-delay: calc(
        var(--animation-order) * 0.1s
    ); /* Stagger animation */
}

@keyframes message-fade-in {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-avatar {
    width: 36px; /* Slightly larger avatar */
    height: 36px;
    border-radius: 50%;
    background-color: var(--chatbot-avatar-bg);
    color: var(--chatbot-avatar-text);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    margin-right: 10px; /* Space between avatar and bubble */
    flex-shrink: 0; /* Prevent avatar from shrinking */
    box-shadow: var(--shadow-sm);
}

.message-content {
    padding: var(--chatbot-padding);
    border-radius: var(--chatbot-border-radius);
    box-shadow: var(--chatbot-message-shadow);
    word-wrap: break-word; /* Ensure long words break */
    overflow-wrap: break-word; /* Standard property */
    hyphens: auto; /* Allow hyphenation */
}

/* Bot Message Specifics */
.message.bot {
    align-self: flex-start;
}
.message.bot .message-content {
    background-color: var(--chatbot-bot-msg-bg);
    color: var(--chatbot-text); /* Use main text color for bot messages */
    border-top-left-radius: 0; /* "Tail" effect */
}

/* User Message Specifics */
.message.user {
    align-self: flex-end;
    flex-direction: row-reverse; /* Avatar on the right */
}
.message.user .message-avatar {
    margin-left: 10px; /* Space on the left for user avatar */
    margin-right: 0;
}
.message.user .message-content {
    background-color: var(--chatbot-user-msg-bg);
    color: var(--chatbot-user-msg-text);
    border-top-right-radius: 0; /* "Tail" effect on the other side */
}

/* Message Content Paragraphs */
.message-content p {
    margin: 0 0 8px 0; /* Space between paragraphs */
    line-height: 1.5;
}
.message-content p:last-child {
    margin-bottom: 0;
}

/* Chatbot Button Styles within Message Content */
.message-content .button-group {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 15px 0 !important;
    padding: 15px !important;
    gap: 10px !important;
    background: rgba(var(--highlight-color-rgb, 0, 123, 255), 0.03) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.1) !important;
}

.message-content .chatbot-button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 10px 16px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #fff !important;
    background: var(--highlight-color, #007bff) !important;
    border: none !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25) !important;
    margin: 2px !important;
    min-width: 120px !important;
    white-space: nowrap !important;
    position: relative !important;
    z-index: 1 !important;
}

.message-content .chatbot-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.35) !important;
    background: var(--highlight-darker, #0056b3) !important;
}

.message-content .chatbot-button:disabled {
    background: #6c757d !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
    box-shadow: none !important;
    transform: none !important;
}

.message-content .llm-model-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.message-content .llm-model-button:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
    transform: translateY(-2px) scale(1.02) !important;
}

.message-content .skip-button {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    color: #fff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.message-content .skip-button:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%) !important;
    transform: translateY(-2px) scale(1.02) !important;
}

/* Ensure buttons are visible and properly spaced */
.message.bot p {
    line-height: 1.6;
}

.message.bot p .button-group {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 20px 0 !important;
    padding: 15px !important;
    background: rgba(var(--highlight-color-rgb, 0, 123, 255), 0.05) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.15) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.message.bot p .chatbot-button {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 5px !important;
    padding: 10px 16px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #ffffff !important;
    background: var(--highlight-color, #007bff) !important;
    border: none !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25) !important;
    min-width: 100px !important;
    text-align: center !important;
    white-space: nowrap !important;
    position: relative !important;
    z-index: 10 !important;
}

.message.bot p .chatbot-button:hover {
    background: var(--highlight-darker, #0056b3) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.35) !important;
}

.message.bot p .llm-model-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #ffffff !important;
}

.message.bot p .llm-model-button:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
}

.message.bot p .skip-button {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    color: #ffffff !important;
}

.message.bot p .skip-button:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%) !important;
}

/* Debug visibility - ensure buttons are not hidden */
button.chatbot-button {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Override any inherited styles that might hide buttons */
.message-content button,
.message-text-content button {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 5 !important;
}

/* High-specificity rules for chatbot buttons */
#chatbot-messages .message.bot .message-text-content .button-group {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 20px 0 !important;
    padding: 15px !important;
    background: rgba(var(--highlight-color-rgb, 0, 123, 255), 0.05) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.15) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

#chatbot-messages .message.bot .message-text-content .chatbot-button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 10px 16px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #ffffff !important;
    background: var(--highlight-color, #007bff) !important;
    border: none !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25) !important;
    margin: 4px !important;
    min-width: 100px !important;
    text-align: center !important;
    white-space: nowrap !important;
    position: relative !important;
    z-index: 10 !important;
    visibility: visible !important;
    opacity: 1 !important;
}

#chatbot-messages .message.bot .message-text-content .chatbot-button:hover {
    background: var(--highlight-darker, #0056b3) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.35) !important;
}

#chatbot-messages .message.bot .message-text-content .llm-model-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #ffffff !important;
}

#chatbot-messages .message.bot .message-text-content .llm-model-button:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
}

#chatbot-messages .message.bot .message-text-content .skip-button {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    color: #ffffff !important;
}

#chatbot-messages .message.bot .message-text-content .skip-button:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%) !important;
}

#chatbot-messages .message.bot .message-text-content .submit-key-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: #ffffff !important;
}

#chatbot-messages .message.bot .message-text-content .submit-key-button:hover {
    background: linear-gradient(135deg, #218838 0%, #1aa085 100%) !important;
}

/* Ensure buttons are not hidden by any parent styles */
#chatbot-messages .message.bot p button,
#chatbot-messages .message.bot div button,
#chatbot-messages button {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Code Blocks within Messages */
.message-content pre {
    background-color: var(--chatbot-code-block-bg);
    color: var(--chatbot-code-block-text);
    padding: 12px; /* More padding */
    border-radius: 8px; /* Rounded corners for code blocks */
    overflow-x: auto; /* Allow horizontal scrolling for long code lines */
    margin: 10px 0; /* Space around code block */
    font-family: "Courier New", Courier, monospace; /* Monospace font */
    font-size: 0.9em; /* Slightly smaller font for code */
    border: 1px solid var(--chatbot-code-block-border);
    white-space: pre-wrap; /* Wrap long lines but preserve formatting */
    word-break: break-all; /* Break long words/tokens if necessary */
}

.message-content code:not(pre code) {
    /* Inline code */
    background-color: var(--chatbot-code-block-bg);
    color: var(--chatbot-code-block-text);
    padding: 2px 5px;
    border-radius: 4px;
    font-size: 0.9em;
}

/* Links within Messages */
.message-content a {
    color: var(--chatbot-link-color);
    text-decoration: underline;
}
.message-content a:hover {
    text-decoration: none;
}

/* Timestamp */
.message-timestamp {
    font-size: 0.75rem;
    color: var(--chatbot-muted-text-color);
    margin-top: 4px;
    text-align: inherit; /* Align with message (left for bot, right for user) */
    padding: 0 5px; /* Add some padding if needed */
}
.message.user .message-timestamp {
    text-align: right;
}
.message.bot .message-timestamp {
    text-align: left;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 5px var(--chatbot-padding); /* Match message padding */
    opacity: 0; /* Start hidden */
    animation: typing-fade-in 0.3s ease forwards;
    margin-left: calc(
        36px + 10px
    ); /* Align with message content (avatar + margin) */
}

@keyframes typing-fade-in {
    to {
        opacity: 1;
    }
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    background-color: var(--chatbot-typing-dot-color);
    border-radius: 50%;
    display: inline-block;
    margin: 0 2px;
    animation: typing-bounce 1.2s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}
.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}
.typing-indicator span:nth-child(3) {
    animation-delay: 0s;
}

@keyframes typing-bounce {
    0%,
    80%,
    100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Input Area */
.input-area {
    display: flex;
    padding: 15px; /* More padding */
    border-top: 1px solid var(--chatbot-border-color);
    background-color: var(--chatbot-bg); /* Ensure it matches container */
    align-items: center; /* Vertically align items */
    gap: 10px; /* Space between input and button */
}

.input-area textarea {
    flex-grow: 1;
    padding: 12px 15px; /* Comfortable padding */
    border: 1px solid var(--chatbot-border-color);
    border-radius: 20px; /* Pill shape */
    resize: none; /* Prevent manual resizing */
    font-family: inherit;
    font-size: 1rem;
    background-color: var(--chatbot-input-bg);
    color: var(--chatbot-text);
    min-height: 24px; /* Start with a single line height */
    max-height: 120px; /* Limit expansion to about 5 lines */
    overflow-y: auto; /* Scroll if content exceeds max-height */
    line-height: 1.4; /* Adjust line height for better readability */
    transition:
        border-color 0.2s ease,
        box-shadow 0.2s ease;
}

.input-area textarea:focus {
    outline: none;
    border-color: var(--highlight-color);
    box-shadow: 0 0 0 2px rgba(var(--highlight-color-rgb), 0.2);
}

.input-area button {
    background-color: var(--chatbot-button-bg);
    color: var(--chatbot-button-text);
    border: none;
    border-radius: 50%; /* Circular button */
    width: 44px; /* Fixed size */
    height: 44px;
    font-size: 1.5rem; /* Icon size */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition:
        background-color 0.2s ease,
        transform 0.1s ease;
    flex-shrink: 0; /* Prevent button from shrinking */
}

.input-area button:hover {
    background-color: var(--chatbot-button-hover-bg);
}

.input-area button:active {
    transform: scale(0.95);
}

.input-area button:disabled {
    background-color: #ccc; /* Muted color for disabled state */
    cursor: not-allowed;
}
[data-theme="dark"] .input-area button:disabled {
    background-color: #555;
}

/* Quick Replies Area */
.quick-replies {
    display: flex;
    flex-wrap: wrap; /* Allow replies to wrap to next line */
    gap: 8px; /* Space between buttons */
    padding: 0 15px 15px 15px; /* Padding around quick replies, bottom matches input area */
    border-top: 1px solid var(--chatbot-border-color); /* Optional: separator line */
    background-color: var(--chatbot-bg);
}

.quick-reply-btn {
    background-color: var(--chatbot-quick-reply-bg);
    color: var(--chatbot-quick-reply-text);
    border: 1px solid var(--chatbot-border-color);
    border-radius: 15px; /* Pill shape */
    padding: 8px 15px;
    font-size: 0.9rem;
    cursor: pointer;
    transition:
        background-color 0.2s ease,
        color 0.2s ease;
}

.quick-reply-btn:hover {
    background-color: var(--chatbot-quick-reply-hover-bg);
    border-color: var(--highlight-color); /* Highlight border on hover */
}

/* Tooltip for API Key Info */
.api-key-tooltip {
    position: relative;
    display: inline-block;
    margin-left: 5px; /* Space from the label */
}

.api-key-tooltip .tooltip-icon {
    font-size: 0.9em; /* Smaller icon */
    color: var(--chatbot-muted-text-color); /* Muted color */
    cursor: help;
    border: 1px solid var(--chatbot-muted-text-color); /* Subtle border */
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition:
        color 0.2s,
        border-color 0.2s;
}
.api-key-tooltip:hover .tooltip-icon {
    color: var(--highlight-color);
    border-color: var(--highlight-color);
}

.api-key-tooltip .tooltip-text {
    visibility: hidden;
    width: 220px; /* Adjust width as needed */
    background-color: var(
        --tooltip-bg,
        #555
    ); /* Use theme variable or fallback */
    color: var(--chatbot-settings-text, #fff);
    text-align: left; /* Align text to the left */
    border-radius: 6px;
    padding: 10px; /* More padding */
    position: absolute;
    z-index: 100; /* Ensure it's above other elements */
    bottom: 125%; /* Position above the icon */
    left: 50%;
    margin-left: -110px; /* Half of width to center */
    opacity: 0;
    transition:
        opacity 0.3s,
        visibility 0.3s;
    font-size: 0.85rem; /* Smaller font for tooltip */
    line-height: 1.4;
    box-shadow: var(--shadow-md);
    animation: tooltip-enter 0.2s ease-out forwards; /* Subtle entrance animation */
}

@keyframes tooltip-enter {
    from {
        transform: translateY(5px);
    }
    to {
        transform: translateY(0);
    }
}

.api-key-tooltip .tooltip-text::after {
    /* Arrow */
    content: "";
    position: absolute;
    top: 100%; /* At the bottom of the tooltip */
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--tooltip-bg, #555) transparent transparent transparent;
}

.api-key-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Chatbot Settings Modal */
.chatbot-settings-modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.6); /* Black w/ opacity for overlay */
    animation: modal-fade-in 0.3s ease;
}

.chatbot-settings-modal-content {
    background-color: var(--chatbot-settings-bg);
    color: var(--chatbot-settings-text);
    margin: 10% auto; /* 10% from the top and centered */
    padding: 0; /* Remove padding, header/footer will have it */
    border: 1px solid var(--chatbot-settings-border);
    width: 90%;
    max-width: 500px; /* Max width */
    border-radius: 10px; /* Rounded corners */
    box-shadow: var(--shadow-lg);
    animation: modal-slide-in 0.4s ease-out;
    display: flex;
    flex-direction: column;
}

@keyframes modal-fade-in {
    from {
        background-color: rgba(0, 0, 0, 0);
    }
    to {
        background-color: rgba(0, 0, 0, 0.6);
    }
}
@keyframes modal-slide-in {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.chatbot-settings-modal-header {
    padding: 15px 20px;
    background-color: var(--chatbot-settings-header-bg);
    color: var(--chatbot-settings-header-text);
    border-bottom: 1px solid var(--chatbot-settings-border);
    border-top-left-radius: 9px; /* Match content radius */
    border-top-right-radius: 9px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chatbot-settings-modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.chatbot-settings-close-btn {
    color: var(--chatbot-settings-header-text);
    font-size: 1.8rem; /* Larger close icon */
    font-weight: bold;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0 5px;
    line-height: 1;
}
.chatbot-settings-close-btn:hover,
.chatbot-settings-close-btn:focus {
    opacity: 0.7;
    text-decoration: none;
}

.chatbot-settings-modal-body {
    padding: 20px 25px; /* More padding in body */
    flex-grow: 1;
    overflow-y: auto; /* Scroll if content is too long */
}

.chatbot-settings-modal-body label {
    display: block;
    margin-bottom: 8px; /* Space below label */
    font-weight: 500; /* Slightly bolder labels */
    font-size: 0.95rem;
    color: var(--chatbot-settings-text);
}

.chatbot-settings-modal-body input[type="text"],
.chatbot-settings-modal-body input[type="password"],
.chatbot-settings-modal-body input[type="number"],
.chatbot-settings-modal-body select,
.chatbot-settings-modal-body textarea {
    width: 100%;
    padding: 10px 12px;
    margin-bottom: 20px; /* More space below input */
    border: 1px solid var(--chatbot-settings-border);
    border-radius: 6px;
    background-color: var(--chatbot-settings-input-bg);
    color: var(--chatbot-settings-text);
    font-size: 1rem;
    box-sizing: border-box;
    transition:
        border-color 0.2s,
        box-shadow 0.2s;
}

.chatbot-settings-modal-body input:focus,
.chatbot-settings-modal-body select:focus,
.chatbot-settings-modal-body textarea:focus {
    outline: none;
    border-color: var(--highlight-color);
    box-shadow: 0 0 0 2px rgba(var(--highlight-color-rgb), 0.25);
}

.chatbot-settings-modal-body textarea {
    min-height: 80px;
    resize: vertical;
}

.chatbot-settings-modal-footer {
    padding: 15px 20px;
    text-align: right;
    border-top: 1px solid var(--chatbot-settings-border);
    background-color: var(--chatbot-settings-bg); /* Match content bg */
    border-bottom-left-radius: 9px; /* Match content radius */
    border-bottom-right-radius: 9px;
}

.chatbot-settings-save-btn {
    background-color: var(--chatbot-settings-button-bg);
    color: var(--chatbot-settings-button-text);
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.2s;
}
.chatbot-settings-save-btn:hover {
    background-color: var(
        --chatbot-button-hover-bg
    ); /* Use general hover for consistency */
}

/* Theme toggle specific styles */
.theme-toggle-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 1rem;
    color: var(--chatbot-settings-text);
}

.theme-toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px; /* Width of the switch */
    height: 26px; /* Height of the switch */
    margin-left: 10px; /* Space from the label text */
}

.theme-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.theme-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc; /* Default off state */
    transition: 0.4s;
    border-radius: 26px; /* Rounded switch */
}
[data-theme="dark"] .theme-slider {
    background-color: #555; /* Darker off state for dark theme */
}

.theme-slider:before {
    position: absolute;
    content: "";
    height: 20px; /* Size of the knob */
    width: 20px;
    left: 3px; /* Position of the knob */
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .theme-slider {
    background-color: var(--highlight-color); /* Active color */
}

input:checked + .theme-slider:before {
    transform: translateX(24px); /* Move knob to the right */
}

/* Helper class for visually hidden but accessible elements */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Styling for notification popups (e.g., "Settings Saved") */
.chatbot-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--chatbot-success-bg-color); /* Use success colors */
    color: var(--chatbot-success-text-color);
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    z-index: 2000; /* Above everything */
    font-size: 0.95rem;
    opacity: 0;
    transition:
        opacity 0.3s ease,
        transform 0.3s ease;
    pointer-events: none; /* Don't intercept clicks when hidden */
}

.chatbot-notification.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
    pointer-events: auto;
}

.chatbot-notification.error {
    background-color: var(--chatbot-error-bg-color);
    color: var(--chatbot-error-text-color);
}

/* Responsive adjustments for chatbot */
@media (max-width: 768px) {
    .chatbot-container {
        height: calc(100vh - 40px); /* Full height minus some margin */
        max-height: none;
        margin: 20px; /* Adjust margin for smaller screens */
        width: auto; /* Take available width */
        max-width: none;
    }

    .chatbot-settings-modal-content {
        margin: 5% auto; /* Adjust margin for smaller screens */
        width: 95%;
    }
}

@media (max-width: 480px) {
    .chatbot-header {
        padding: 12px 15px;
    }
    .chatbot-header h2 {
        font-size: 1rem;
    }
    .chatbot-settings-icon {
        font-size: 1.3rem;
    }
    .messages-area {
        padding: 10px;
    }
    .input-area {
        padding: 10px;
        gap: 8px;
    }
    .input-area textarea {
        padding: 10px 12px;
        border-radius: 18px;
    }
    .input-area button {
        width: 40px;
        height: 40px;
        font-size: 1.3rem;
    }
    .quick-replies {
        padding: 0 10px 10px 10px;
    }
    .quick-reply-btn {
        padding: 6px 12px;
        font-size: 0.85rem;
    }
    .chatbot-settings-modal-body {
        padding: 15px 20px;
    }
    .chatbot-settings-modal-footer {
        padding: 12px 15px;
    }
    .chatbot-settings-save-btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}

/* Portfolio Page Specific Styles */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(
        auto-fit,
        minmax(300px, 1fr)
    ); /* Responsive grid */
    gap: 25px; /* Space between items */
    padding: 20px 0;
}

.portfolio-item {
    background-color: var(--card-bg-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow-md);
    transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;
    display: flex;
    flex-direction: column; /* Align content vertically */
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.portfolio-item img.project-thumbnail {
    width: 100%;
    height: 200px; /* Fixed height for thumbnails */
    object-fit: cover; /* Cover the area, might crop */
    border-radius: 8px; /* Rounded corners for image */
    margin-bottom: 15px;
    border: 1px solid var(--border-color); /* Subtle border for image */
}

.portfolio-item h3 {
    font-size: 1.5rem;
    color: var(--highlight-color);
    margin-top: 0;
    margin-bottom: 10px;
}

.portfolio-item p.project-description {
    font-size: 1rem;
    color: var(--text-color);
    line-height: 1.6;
    flex-grow: 1; /* Allow description to take available space */
    margin-bottom: 15px;
}

.portfolio-item .project-tags {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.portfolio-item .project-tags .tag {
    background-color: var(--nav-hover-color); /* Use a subtle background */
    color: var(--text-color);
    padding: 5px 10px;
    border-radius: 15px; /* Pill shape */
    font-size: 0.85rem;
}
[data-theme="dark"] .portfolio-item .project-tags .tag {
    background-color: var(--nav-hover-color);
    color: var(--text-color);
}

.portfolio-item .project-links {
    margin-top: auto; /* Push links to the bottom */
    display: flex;
    gap: 10px; /* Space between links */
}

.portfolio-item .project-links a {
    display: inline-block;
    padding: 8px 15px;
    background-color: var(--button-bg-color);
    color: var(--button-text-color);
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
    text-align: center;
}

.portfolio-item .project-links a:hover {
    background-color: var(--button-hover-bg-color);
}

.portfolio-item .project-links a.secondary-link {
    background-color: transparent;
    color: var(--highlight-color);
    border: 1px solid var(--highlight-color);
}
.portfolio-item .project-links a.secondary-link:hover {
    background-color: rgba(
        var(--highlight-color-rgb),
        0.1
    ); /* Light highlight bg on hover */
}

/* Filter bar for portfolio */
.portfolio-filter-bar {
    margin-bottom: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center; /* Center filter buttons */
}

.portfolio-filter-bar button {
    padding: 8px 18px;
    font-size: 0.95rem;
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 20px; /* Pill shape */
    cursor: pointer;
    transition:
        background-color 0.2s ease,
        color 0.2s ease,
        border-color 0.2s ease;
}

.portfolio-filter-bar button:hover {
    background-color: var(--nav-hover-color);
    border-color: var(--highlight-color);
}

.portfolio-filter-bar button.active {
    background-color: var(--highlight-color);
    color: var(--button-text-color);
    border-color: var(--highlight-color);
    font-weight: 500;
}
[data-theme="dark"] .portfolio-filter-bar button.active {
    color: var(
        --button-text-color
    ); /* Ensure text is readable on dark active button */
}

/* General loading spinner (can be used on portfolio or other pages) */
.loading-spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    min-height: 200px; /* Ensure it takes some space */
}

.loading-spinner {
    border: 5px solid var(--nav-hover-color); /* Light grey */
    border-top: 5px solid var(--highlight-color); /* Blue */
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Ensure no double borders on cards if they are also links */
a.portfolio-item,
a.result-box,
a.chart-box {
    text-decoration: none; /* Remove underline from linked cards */
    color: inherit; /* Inherit text color */
}

/* Donut Chart Styling */
#donut-chart-container {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: #eee; /* Placeholder for the track of the donut */
    position: relative; /* Needed for pseudo-elements or inner elements for the fill/hole */
    margin: 20px auto; /* Centering and spacing */
    display: flex; /* To help center potential inner text or icons */
    align-items: center;
    justify-content: center;
}

.donut-chart {
    width: 100%;
    height: 100%;
    border-radius: 50%; /* To make the gradient circular */
    position: relative; /* For positioning the inner text */
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box; /* Ensures padding/border don't expand it beyond 100% */
}

/* ✨ PREMIUM DONUT CHART TEXT - FIXED VISIBILITY ✨ */
.donut-chart-text {
    position: relative !important;
    z-index: 10 !important;
    font-size: 16px !important;
    font-weight: 900 !important;
    color: #2c3e50 !important;
    text-shadow:
        0 1px 2px rgba(255, 255, 255, 0.9),
        0 0 8px rgba(255, 255, 255, 0.7) !important;
    pointer-events: none;
    display: inline-block;
    line-height: 1;
}

/* Dark theme text fix */
[data-theme="dark"] .donut-chart-text {
    color: #ffffff !important;
    text-shadow:
        0 1px 2px rgba(0, 0, 0, 0.9),
        0 0 12px rgba(121, 40, 202, 0.8),
        0 0 20px rgba(0, 212, 255, 0.4) !important;
}

/* Styles for the SVG Donut Chart */
.donut-chart-container {
    /* This is the div holding the SVG */
    width: 120px; /* Or desired size for the container */
    height: 120px; /* Or desired size for the container */
    margin: 15px auto; /* Centering and spacing */
    position: relative; /* If needed for absolute positioning of other elements around it */
}

.donut-chart-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg); /* Start the progress from the top */
    display: block; /* Ensure it takes up space */
}

.donut-track {
    fill: transparent;
    stroke: #e6e6e6; /* Light grey for the track */
    stroke-width: 10; /* Adjust thickness of the donut ring */
}

.donut-progress {
    fill: transparent;
    stroke: var(
        --highlight-color,
        #007bff
    ); /* Use a theme color or a specific color */
    stroke-width: 10; /* Must match track's stroke-width */
    stroke-linecap: round; /* Optional: for rounded ends of the progress bar */
    transition: stroke-dashoffset 0.5s ease-out; /* Smooth animation for changes */
}

.donut-text {
    fill: var(--text-color, #333); /* Text color */
    font-family: Arial, sans-serif;
    font-size: 20px; /* Adjust as needed */
    font-weight: bold;
    text-anchor: middle; /* Horizontally center */
    dominant-baseline: middle; /* Vertically center (approximate for SVG text) */
    transform: rotate(90deg) translate(0, -100px) scale(1, -1); /* Counter-rotate and reposition text */
    /* The translate and scale are to correct orientation due to SVG's rotation */
    /* You might need to fine-tune translate values based on font and SVG size */
}

/* =============================================== */
/* === D3.js SVG Score Gauge Styles === */
/* =============================================== */

/* Container for the SVG gauge chart within a message */
.score-gauge-chart-container {
    display: flex;
    justify-content: center; /* Centers the SVG chart horizontally within this div */
    align-items: center;
    margin-top: 12px; /* Space between main text and chart */
    margin-bottom: 8px; /* Space between chart and risk text (if any) */
    width: 100%; /* Takes the width of the parent <p> bubble */
    min-height: 80px; /* Minimum height for the chart SVG */
    /* border: 1px dashed blue; /* TEMPORARY: For visualizing the container */
}

/* The SVG element itself, created by D3 */
.score-gauge-svg {
    display: block; /* Removes extra bottom space often added to inline SVGs */
    /* Width and height are set via attributes in the JavaScript (renderScoreGaugeSVG) */
    /* Example: width="80" height="80" */
    overflow: visible; /* Ensures rounded stroke-linecap isn't clipped if near edge */
}

/* The background track of the gauge (the full circle) */
.gauge-background {
    /* stroke: #e9ecef; /* Fallback if --chatbot-input-bg CSS var not found or JS fails */
    /* stroke-width is set in JS */
    /* fill: none; is set in JS */
    opacity: 0.7; /* Make the background track slightly more subtle */
}

/* The foreground arc representing the score */
.gauge-foreground {
    /* stroke: #198754; /* Fallback, actual color set dynamically by JS from analysisData.color */
    /* stroke-width is set in JS */
    /* fill: none; is set in JS */
    /* stroke-linecap: round; is set in JS */
    /* stroke-dasharray and stroke-dashoffset are set and animated in JS */
    transition:
        stroke-dashoffset 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        /* Smooth animation for the arc */ stroke 0.5s ease-in-out; /* Smooth transition if color changes */
}

/* The text element in the center of the gauge (contains score and % sign) */
.gauge-center-text {
    font-family: var(
        --bs-body-font-family,
        system-ui,
        -apple-system,
        "Segoe UI",
        Roboto,
        "Helvetica Neue",
        Arial,
        "Noto Sans",
        "Liberation Sans",
        sans-serif,
        "Apple Color Emoji",
        "Segoe UI Emoji",
        "Segoe UI Symbol",
        "Noto Color Emoji"
    ); /* Use Bootstrap's body font or fallbacks */
    /* text-anchor: middle; is set in JS */
    /* dominant-baseline: middle; is set in JS */
    transition: fill 0.5s ease-in-out; /* Smooth transition if text color changes with score color */
}

/* The tspan for the score number */
.gauge-score {
    /* font-size is set in JS (e.g., 18px) */
    /* font-weight is set in JS (e.g., 600) */
    /* fill color is set dynamically in JS to match analysisData.color */
    letter-spacing: -0.5px; /* Optional: slightly tighter spacing for numbers */
}

/* The tspan for the '%' symbol */

/* Style for the risk text displayed below the gauge */
.score-gauge-risk-text {
    text-align: center;
    font-size: 0.8rem; /* Slightly smaller than default chat text */
    font-weight: 500; /* Medium weight */
    margin-top: -2px; /* Pull slightly closer to the gauge if desired */
    margin-bottom: 5px; /* Space below the risk text */
    padding: 0 5px; /* Some padding if text is long */
    line-height: 1.3; /* Adjust line height for readability */
    width: 100%; /* Ensure it uses the available width for centering */
    /* Color for risk text is set dynamically via inline style in JavaScript (addMessage) */
    /* e.g., riskTextDiv.style.color = analysisData.color; */
}

/* Example of using Bootstrap alert colors if you want to override JS-set color for risk text */
/* (This is an alternative, the JS currently sets the color directly) */
/*
.score-gauge-risk-text.text-success { color: var(--bs-success); }
.score-gauge-risk-text.text-warning { color: var(--bs-warning); }
.score-gauge-risk-text.text-danger  { color: var(--bs-danger);  }
.score-gauge-risk-text.text-info    { color: var(--bs-info);    }
*/

/* --- Ensure message content div allows for centering --- */
/* This is likely already in your chatbot CSS, but ensure it behaves well with the gauge */
.message-content {
    /* ... your existing styles ... */
    display: flex;
    flex-direction: column; /* Stacks text and chart vertically */
    align-items: flex-start; /* Default for bot messages, text aligns left */
}

/* If bot messages have text aligned left, and you want gauge centered within message bubble */
/* The .score-gauge-chart-container itself uses flex to center the SVG within its own bounds */

/* Financial Features Styles */
.financial-template {
    display: none;
    width: 100%;
    padding: 15px;
    background-color: var(--bg-light, #f8f9fa);
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.financial-options {
    margin-bottom: 15px;
}

.financial-options h4 {
    font-size: 16px;
    margin-bottom: 10px;
    color: var(--text-color, #333);
}

.financial-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.financial-button {
    padding: 8px 15px;
    background-color: var(--primary-color, #007bff);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.financial-button:hover {
    background-color: var(--primary-dark, #0056b3);
}

.financial-button i {
    margin-right: 8px;
}

.financial-button.active {
    background-color: var(--primary-dark, #0056b3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.financial-container {
    background-color: white;
    border-radius: 6px;
    padding: 15px;
    min-height: 200px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Financial Statements Styles */
.financial-table-container {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.financial-table {
    width: 100%;
    border-collapse: collapse;
}

.financial-table th {
    position: sticky;
    top: 0;
    background-color: var(--primary-color, #007bff);
    color: white;
    padding: 10px;
    text-align: left;
}

.financial-table td {
    padding: 8px 10px;
    border-bottom: 1px solid #eee;
}

.financial-table .item-name {
    font-weight: 500;
}

.financial-table .item-value {
    text-align: right;
    font-family: monospace;
    font-size: 13px;
}

.financial-visualization {
    margin-top: 30px;
}

.visualization-title {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--text-color, #333);
}

.chart-container {
    width: 100%;
    height: 400px;
    background-color: var(--bg-light, #f8f9fa);
    border-radius: 6px;
    padding: 15px;
}

/* Sentiment Analysis Styles */
.sentiment-timeline {
    margin-bottom: 30px;
}

.section-title {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--text-color, #333);
}

.sentiment-summary {
    font-size: 15px;
    margin-top: 15px;
    color: var(--text-color, #333);
    font-style: italic;
}

.news-item {
    background-color: var(--bg-light, #f8f9fa);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
}

.news-item:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.news-item.positive {
    border-left: 4px solid #4caf50;
}

.news-item.negative {
    border-left: 4px solid #f44336;
}

.news-item.neutral {
    border-left: 4px solid #9e9e9e;
}

.news-title {
    font-size: 16px;
    margin-bottom: 5px;
    color: var(--text-color, #333);
}

.news-date {
    font-size: 12px;
    color: var(--text-muted, #6c757d);
    margin-bottom: 10px;
}

.news-summary {
    font-size: 14px;
    margin-bottom: 10px;
}

.sentiment-indicator {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.sentiment-label {
    font-size: 13px;
    margin-right: 10px;
    color: var(--text-muted, #6c757d);
}

.sentiment-bar {
    height: 12px;
    width: 150px;
    background-color: #eee;
    border-radius: 6px;
    overflow: hidden;
    margin-right: 10px;
}

.sentiment-fill {
    height: 100%;
}

.sentiment-fill.positive {
    background-color: #4caf50;
}

.sentiment-fill.negative {
    background-color: #f44336;
}

.sentiment-fill.neutral {
    background-color: #9e9e9e;
}

.sentiment-value {
    font-size: 13px;
    font-weight: 500;
}

/* D3 Chart Styles */
.line {
    fill: none;
    stroke-width: 2px;
}

.area {
    opacity: 0.2;
}

.dot {
    fill: #fff;
    stroke-width: 2px;
}

.x-axis path,
.y-axis path,
.x-axis line,
.y-axis line {
    stroke: #ddd;
}

.x-axis text,
.y-axis text {
    font-size: 12px;
    fill: #666;
}

.legend-text {
    font-size: 12px;
    fill: #666;
}

.tooltip {
    position: absolute;
    padding: 10px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    pointer-events: none;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

/* Portfolio Tracker Styles */
.portfolio-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.portfolio-chart {
    grid-column: span 2;
}

.portfolio-metrics {
    background-color: var(--bg-light, #f8f9fa);
    border-radius: 6px;
    padding: 15px;
}

.portfolio-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.portfolio-table th {
    background-color: var(--primary-color, #007bff);
    color: white;
    padding: 10px;
    text-align: left;
}

.portfolio-table td {
    padding: 8px 10px;
    border-bottom: 1px solid #eee;
}

.positive-value {
    color: #4caf50;
}

.negative-value {
    color: #f44336;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .financial-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .portfolio-overview {
        grid-template-columns: 1fr;
    }

    .portfolio-chart {
        grid-column: span 1;
    }
}

/* Enhanced D3.js Rating Charts with Scroll Animations */
.rating-visualization-container,
.enhanced-rating-charts-container {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
    transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
    margin: 20px 0;
    padding: 25px;
    background: var(--card-bg-color, white);
    border-radius: 16px;
    box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.08));
    border: 1px solid var(--border-color, #e0e0e0);
    animation: fadeInUp 0.8s ease-out forwards;
}

.enhanced-rating-charts-container {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.rating-visualization-container.visible,
.enhanced-rating-charts-container.visible {
    opacity: 1;
    transform: translateY(0px) scale(1);
}

/* 🌟 SPECTACULAR OVERALL INVESTMENT RATING - THE SHOWSTOPPER 🌟 */
.overall-rating-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 35px;
    margin-bottom: 40px;
    padding: 60px 50px;
    background: linear-gradient(
        135deg,
        rgba(0, 212, 255, 0.2) 0%,
        rgba(255, 107, 53, 0.15) 25%,
        rgba(0, 255, 136, 0.2) 50%,
        rgba(121, 40, 202, 0.15) 75%,
        rgba(0, 212, 255, 0.2) 100%
    );
    background-size: 600% 600%;
    border-radius: 40px;
    border: 4px solid transparent;
    background-clip: padding-box;
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
    box-shadow:
        0 40px 120px rgba(0, 212, 255, 0.3),
        0 25px 80px rgba(255, 107, 53, 0.2),
        0 15px 40px rgba(0, 0, 0, 0.15),
        inset 0 3px 0 rgba(255, 255, 255, 0.4),
        inset 0 -3px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: visible !important; /* FIXED: Remove clipping */
    cursor: pointer;
    transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    animation: spectacularPulse 5s ease-in-out infinite;
    min-height: 320px;
    transform-style: preserve-3d;
    perspective: 1200px;
}

/* Enhanced border glow effect */
.overall-rating-section::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(
        45deg,
        rgba(0, 212, 255, 0.8),
        rgba(255, 107, 53, 0.8),
        rgba(0, 255, 136, 0.8),
        rgba(121, 40, 202, 0.8),
        rgba(0, 212, 255, 0.8)
    );
    background-size: 400% 400%;
    border-radius: 44px;
    z-index: -1;
    animation: borderGlow 6s linear infinite;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.overall-rating-section:hover::before {
    opacity: 1;
}

.overall-rating-section.exceptional-rating::before {
    opacity: 0.8;
    animation: borderGlow 3s linear infinite;
}

@keyframes spectacularPulse {
    0%, 100% {
        transform: translateZ(0) scale(1);
        background-position: 0% 50%;
        box-shadow:
            0 40px 120px rgba(0, 212, 255, 0.3),
            0 25px 80px rgba(255, 107, 53, 0.2),
            0 15px 40px rgba(0, 0, 0, 0.15),
            inset 0 3px 0 rgba(255, 255, 255, 0.4);
    }
    50% {
        transform: translateZ(8px) scale(1.02);
        background-position: 100% 50%;
        box-shadow:
            0 50px 140px rgba(0, 212, 255, 0.4),
            0 30px 100px rgba(255, 107, 53, 0.3),
            0 20px 60px rgba(0, 255, 136, 0.2),
            inset 0 4px 0 rgba(255, 255, 255, 0.5);
    }
}

@keyframes borderGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.overall-rating-section:hover {
    transform: translateY(-10px) scale(1.05);
    animation-duration: 3s;
    box-shadow:
        0 60px 160px rgba(0, 212, 255, 0.4),
        0 40px 120px rgba(255, 107, 53, 0.3),
        0 25px 80px rgba(0, 255, 136, 0.2),
        inset 0 4px 0 rgba(255, 255, 255, 0.6);
}

.overall-chart-wrapper {
    position: relative;
}

/* 🌟 PREMIUM OVERALL RATING DETAILS 🌟 */
.overall-rating-details {
    text-align: center;
    position: relative;
    z-index: 2;
}

.rating-category {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.8), 0 0 8px rgba(0,212,255,0.4) !important;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.rating-score-text {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 10px;
    color: #ffffff !important;
    text-shadow: 0 3px 6px rgba(0,0,0,0.9), 0 0 20px rgba(0,212,255,0.6) !important;
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    position: relative;
    display: inline-flex;
    align-items: baseline;
    background: transparent;
    border: none;
    filter: none;
}

.percent-sign {
    font-size: 2rem;
    opacity: 0.9;
    margin-left: 3px;
}

.rating-description {
    font-size: 1rem;
    font-weight: 500;
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.8) !important;
    margin-top: 8px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.6s ease-out 0.3s;
}

/* Interactive Metric Tiles */
.metric-tiles-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 20px;
    width: 100%;
}

.metric-tile {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 15px 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.metric-tile:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(0, 212, 255, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.metric-tile-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 5px;
    font-weight: 500;
}

.metric-tile-value {
    font-size: 1.2rem;
    color: #ffffff;
    font-weight: 700;
}

.component-ratings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 30px;
    margin-top: 30px;
    padding: 20px;
    overflow: visible;
}

/* 💎 PREMIUM COMPONENT RATING CARDS 💎 */
.component-rating-item {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.85) 100%
    );
    padding: 30px 25px 50px 25px; /* Extra bottom padding for score positioning */
    border-radius: 24px;
    text-align: center;
    border: 2px solid transparent;
    background-clip: padding-box;
    backdrop-filter: blur(15px);
    box-shadow:
        0 15px 50px rgba(0, 0, 0, 0.12),
        0 8px 25px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    cursor: pointer;
    position: relative;
    overflow: visible !important; /* FIXED: Remove clipping */
    opacity: 0;
    transform: scale(0.8) translateY(40px) rotateX(15deg);
    perspective: 1000px;
    transform-style: preserve-3d;
    margin: 25px 15px; /* Add margin to prevent clipping */
    /* FIXED: Proper layout structure */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 200px; /* Consistent height */
    height: auto; /* Allow natural height */
}

.component-rating-item:hover {
    transform: scale(1.15) translateY(-15px) rotateX(-10deg) rotateY(-3deg);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.25),
        0 15px 30px rgba(0, 0, 0, 0.15),
        0 0 0 2px var(--comp-color, #007bff);
    filter: drop-shadow(0 0 25px var(--comp-color-transparent, #007bff40));
    animation: backgroundFloat 3s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0% { transform: scale(1.15) translateY(-15px) rotateX(-10deg) rotateY(-3deg) translate(0, 0); }
    25% { transform: scale(1.15) translateY(-15px) rotateX(-10deg) rotateY(-3deg) translate(2px, -5px); }
    50% { transform: scale(1.15) translateY(-15px) rotateX(-10deg) rotateY(-3deg) translate(-2px, 0px); }
    75% { transform: scale(1.15) translateY(-15px) rotateX(-10deg) rotateY(-3deg) translate(1px, -7px); }
    100% { transform: scale(1.15) translateY(-15px) rotateX(-10deg) rotateY(-3deg) translate(0, 0); }
}

/* Shimmer effect on hover */
.component-rating-item::before {
    content: "";
    position: absolute;
    top: -2px;
    left: -100%;
    width: 100%;
    height: calc(100% + 4px);
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    pointer-events: none;
    z-index: 1;
    transition: left 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    border-radius: 24px;
}

.component-rating-item:hover::before {
    left: 100%;
}

/* Glow border effect */
.component-rating-item::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(
        45deg,
        var(--comp-color, #007bff),
        rgba(255, 255, 255, 0.5),
        var(--comp-color, #007bff)
    );
    border-radius: 27px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
    filter: blur(8px);
}

.component-rating-item:hover::after {
    opacity: 1;
}

/* FIXED: Premium component header structure */
.component-header {
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    position: relative;
    z-index: 2;
}

/* FIXED: Chart wrapper with proper spacing */
.component-chart-wrapper {
    height: 100px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    flex: 1; /* Allow it to grow and fill space */
}

/* Ensure SVG charts are properly centered and sized */
.component-chart-wrapper svg {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    height: auto;
}

/* FIXED: Premium score display positioning */
.component-score-display {
    font-size: 1.3rem;
    font-weight: 800;
    color: #2c3e50 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.9);
    padding: 5px 12px;
    border-radius: 12px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    backdrop-filter: blur(10px);
    /* FIXED: Absolute positioning at bottom */
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    display: inline-flex;
    align-items: baseline;
    justify-content: center;
    white-space: nowrap;
}

/* Dark theme score display */
[data-theme="dark"] .component-score-display {
    color: #ffffff !important;
    background: rgba(43, 43, 61, 0.9);
    border-color: rgba(121, 40, 202, 0.4);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Component card content styling */
.component-rating-item .component-name {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #2c3e50 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    /* Remove transparent text fill for better visibility */
    background: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    z-index: 2;
}

/* Dark theme component name */
[data-theme="dark"] .component-rating-item .component-name {
    color: #ffffff !important;
    background: linear-gradient(45deg, #ffffff, #e0e0e0);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Premium microinteractions */
.component-rating-item:hover .component-chart-wrapper svg {
    transform: scale(1.1) rotate(5deg);
    transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

/* Success celebration for exceptional ratings */
.overall-rating-section.exceptional-rating {
    animation: exceptionalCelebration 3s ease-in-out infinite;
}

@keyframes exceptionalCelebration {
    0%, 100% {
        transform: translateZ(0) scale(1);
        box-shadow:
            0 40px 120px rgba(0, 212, 255, 0.3),
            0 25px 80px rgba(255, 107, 53, 0.2),
            0 15px 40px rgba(0, 0, 0, 0.15);
    }
    25% {
        transform: translateZ(5px) scale(1.02) rotateY(2deg);
        box-shadow:
            0 50px 140px rgba(0, 212, 255, 0.4),
            0 30px 100px rgba(255, 107, 53, 0.3),
            0 20px 60px rgba(0, 255, 136, 0.2);
    }
    50% {
        transform: translateZ(8px) scale(1.03);
        box-shadow:
            0 60px 160px rgba(0, 212, 255, 0.5),
            0 35px 120px rgba(255, 107, 53, 0.4),
            0 25px 80px rgba(0, 255, 136, 0.3);
    }
    75% {
        transform: translateZ(5px) scale(1.02) rotateY(-2deg);
        box-shadow:
            0 50px 140px rgba(0, 212, 255, 0.4),
            0 30px 100px rgba(255, 107, 53, 0.3),
            0 20px 60px rgba(0, 255, 136, 0.2);
    }
}

/* Enhanced shimmer animation */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced focus states for accessibility */
.component-rating-item:focus,
.overall-rating-section:focus {
    outline: 3px solid rgba(0, 212, 255, 0.6);
    outline-offset: 4px;
}

.rating-chart-svg .background-circle {
    fill: none;
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 3;
}

.rating-chart-svg .foreground-circle {
    fill: none;
    stroke-linecap: round;
    transform-origin: center;
    transform: rotate(-90deg);
    transition: stroke-dashoffset 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.1));
}

.rating-chart-svg .score-text {
    text-anchor: middle;
    dominant-baseline: middle;
    font-weight: bold;
    font-size: 12px;
    fill: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.rating-chart-svg .score-text .percent-sign {
    font-size: 0.7em;
    opacity: 0.8;
}

/* Message Rating Charts */
.message-rating-charts {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: rgba(var(--highlight-color-rgb, 0, 123, 255), 0.02);
    border-radius: 12px;
    border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.1);
}

.rating-chart-container-message {
    text-align: center;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.rating-chart-container-message .chart-label {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-color);
    opacity: 0.8;
}

.rating-chart-container-message .component-chart {
    position: relative;
    display: inline-block;
}

.component-chart-label {
    font-size: 0.75rem;
    margin-top: 5px;
    color: var(--text-color);
    opacity: 0.7;
    font-weight: 500;
}

/* Enhanced Message Styling */
.message.bot p strong {
    color: var(--highlight-color, #007bff);
    font-weight: 600;
}

.message.bot p em {
    color: var(--text-color);
    font-style: italic;
}

.message.bot p {
    line-height: 1.6;
    color: var(--text-color);
}

.investment-analysis-content {
    background: var(--card-bg-color, white);
    padding: 20px;
    border-radius: 12px;
    margin: 15px 0;
    border: 1px solid var(--border-color, #e0e0e0);
}

/* Scroll Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.message.visible {
    animation: slideInLeft 0.5s ease-out;
}

.rating-visualization-container.visible {
    animation: fadeInUp 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.component-rating-item.visible {
    animation: scaleIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Dark theme adjustments for rating charts */
[data-theme="dark"] .rating-visualization-container,
[data-theme="dark"] .enhanced-rating-charts-container {
    background: var(--chatbot-bg, #1e1e2d);
    border-color: var(--chatbot-border-color, #444466);
}

[data-theme="dark"] .component-rating-item {
    background: var(--chatbot-input-bg, #2b2b3d);
    border-color: var(--chatbot-border-color, #444466);
}

[data-theme="dark"] .overall-rating-section {
    background: linear-gradient(
        135deg,
        rgba(121, 40, 202, 0.2) 0%,
        rgba(255, 0, 128, 0.15) 25%,
        rgba(0, 212, 255, 0.2) 50%,
        rgba(255, 107, 53, 0.15) 75%,
        rgba(121, 40, 202, 0.2) 100%
    );
    border-color: rgba(121, 40, 202, 0.4);
    box-shadow:
        0 40px 120px rgba(121, 40, 202, 0.3),
        0 25px 80px rgba(255, 0, 128, 0.2),
        0 15px 40px rgba(0, 0, 0, 0.3),
        inset 0 3px 0 rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .component-rating-item {
    background: linear-gradient(
        135deg,
        rgba(43, 43, 61, 0.95) 0%,
        rgba(68, 68, 102, 0.9) 100%
    );
    border-color: rgba(121, 40, 202, 0.3);
    box-shadow:
        0 15px 50px rgba(121, 40, 202, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .message-rating-charts {
    background: rgba(121, 40, 202, 0.05);
    border-color: rgba(121, 40, 202, 0.1);
}

/* === COMPREHENSIVE CONTRAST FIX FOR ENTIRE SITE === */
/* Fix all remaining hardcoded colors for proper dark/light mode support */

/* NUCLEAR OPTION - FORCE ALL TEXT TO USE THEME COLORS */
* {
    color: var(--text-color) !important;
}

/* Specific overrides for different text types */
h1, h2, h3, h4, h5, h6,
p, span, div, li, td, th,
label, legend, caption,
.text, .title, .subtitle,
.content, .description,
.value, .label, .metric {
    color: var(--text-color) !important;
}

/* Muted text elements */
.text-muted, .muted, .secondary,
.subtitle, .description,
small, .small,
.text-secondary {
    color: var(--text-muted-color) !important;
}

/* Links */
a:not(.btn):not(.button) {
    color: var(--highlight-color) !important;
}

/* Positive/negative values */
.positive-value, .green {
    color: var(--positive-color) !important;
}

.negative-value, .red {
    color: var(--negative-color) !important;
}

/* Keep white text on colored backgrounds */
.btn-primary, .btn-success, .btn-danger, .btn-warning, .btn-info,
.badge, .alert-success, .alert-danger, .alert-warning, .alert-info,
.bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info,
.metric-change.positive, .metric-change.negative,
.ticker-badge, .category-icon, .verdict-icon, .card-icon,
.portfolio-table th, .d3-slice-label, #ratingText {
    color: #ffffff !important;
    fill: #ffffff !important;
}

/* SVG text elements */
svg text:not(.d3-slice-label):not(#ratingText):not(.keep-white) {
    fill: var(--text-color) !important;
}

/* Fix tooltip backgrounds */
.tooltip {
    background: var(--card-bg-color) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

/* Fix loading indicators */
.loading-text {
    color: var(--text-color) !important;
}

/* Fix hardcoded white/black colors */
.donut-chart-text {
    color: var(--text-color) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

[data-theme="dark"] .donut-chart-text {
    color: var(--text-color) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.9) !important;
}

/* Fix financial container backgrounds */
.financial-container {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
}

/* Fix news items */
.news-item {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
}

/* Fix sentiment bars */
.sentiment-bar {
    background-color: var(--nav-hover-color) !important;
}

/* Fix chart tooltips */
.chart-tooltip {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

/* Fix portfolio metrics */
.portfolio-metrics {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
}

/* Fix table headers */
.portfolio-table th {
    background-color: var(--highlight-color) !important;
    color: #ffffff !important;
}

/* Fix positive/negative values */
.positive-value {
    color: var(--positive-color) !important;
}

.negative-value {
    color: var(--negative-color) !important;
}

/* Fix metric tiles */
.metric-tile-label {
    color: var(--text-muted-color) !important;
}

.metric-tile-value {
    color: var(--text-color) !important;
}

/* Fix component scores */
.component-score-display {
    color: var(--text-color) !important;
    background: var(--card-bg-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .component-score-display {
    color: var(--text-color) !important;
    background: var(--card-bg-color) !important;
    border-color: var(--border-color) !important;
}

/* Fix component names */
.component-name {
    color: var(--text-color) !important;
}

[data-theme="dark"] .component-name {
    color: var(--text-color) !important;
}

/* Fix SVG text elements */
svg text {
    fill: var(--text-color) !important;
}

/* Keep white text on colored backgrounds */
.metric-change.positive,
.metric-change.negative,
.ticker-badge,
.category-icon,
.verdict-icon,
.card-icon,
.portfolio-table th,
button:not(.secondary-link) {
    color: #ffffff !important;
}

/* Fix disabled button states */
.input-area button:disabled {
    background-color: var(--nav-hover-color) !important;
    color: var(--text-muted-color) !important;
}

[data-theme="dark"] .input-area button:disabled {
    background-color: var(--nav-hover-color) !important;
    color: var(--text-muted-color) !important;
}

/* Fix theme slider */
.theme-slider {
    background-color: var(--nav-hover-color) !important;
}

[data-theme="dark"] .theme-slider {
    background-color: var(--nav-hover-color) !important;
}

.theme-slider:before {
    background-color: #ffffff !important;
}

/* Fix modal overlays */
.chatbot-settings-modal {
    background-color: rgba(0, 0, 0, 0.6) !important;
}

/* Fix all remaining hardcoded colors */
.gauge-info,
.gauge-info span {
    color: var(--text-color) !important;
}

/* === COMPREHENSIVE SEMANTIC COLOR SYSTEM === */
/* Preserve semantic colors across all themes - these should NEVER change based on theme */
.positive,
.positive-value,
.green,
.profit,
.gain,
.up,
.increase,
.metric-change.positive,
.performance-positive,
.change-positive {
    color: var(--positive-color) !important;
}

.negative,
.negative-value,
.red,
.loss,
.down,
.decrease,
.metric-change.negative,
.performance-negative,
.change-negative {
    color: var(--negative-color) !important;
}

.warning,
.neutral,
.metric-change.neutral,
.performance-neutral {
    color: var(--highlight-secondary) !important;
}

/* Percentage and currency values with semantic meaning */
.percentage.positive,
.currency.positive,
span[class*="positive"],
div[class*="positive"] {
    color: var(--positive-color) !important;
}

.percentage.negative,
.currency.negative,
span[class*="negative"],
div[class*="negative"] {
    color: var(--negative-color) !important;
}

/* Portfolio specific semantic colors */
.portfolio-gain,
.portfolio-profit,
.return-positive {
    color: var(--positive-color) !important;
}

.portfolio-loss,
.portfolio-deficit,
.return-negative {
    color: var(--negative-color) !important;
}

/* Dark mode text contrast improvements */
[data-theme="dark"] {
    color: var(--text-color);
}

[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6,
[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] div,
[data-theme="dark"] label {
    color: var(--text-color);
}

/* Ensure semantic colors are preserved in BOTH themes */
/* Dark mode semantic color preservation */
[data-theme="dark"] .positive,
[data-theme="dark"] .positive-value,
[data-theme="dark"] .green,
[data-theme="dark"] .profit,
[data-theme="dark"] .gain,
[data-theme="dark"] .up,
[data-theme="dark"] .increase,
[data-theme="dark"] .metric-change.positive,
[data-theme="dark"] .performance-positive,
[data-theme="dark"] .portfolio-gain,
[data-theme="dark"] .return-positive {
    color: var(--positive-color) !important;
}

[data-theme="dark"] .negative,
[data-theme="dark"] .negative-value,
[data-theme="dark"] .red,
[data-theme="dark"] .loss,
[data-theme="dark"] .down,
[data-theme="dark"] .decrease,
[data-theme="dark"] .metric-change.negative,
[data-theme="dark"] .performance-negative,
[data-theme="dark"] .portfolio-loss,
[data-theme="dark"] .return-negative {
    color: var(--negative-color) !important;
}

[data-theme="dark"] .warning,
[data-theme="dark"] .neutral,
[data-theme="dark"] .metric-change.neutral {
    color: var(--highlight-secondary) !important;
}

/* Light mode semantic color preservation */
:root .positive,
:root .positive-value,
:root .green,
:root .profit,
:root .gain,
:root .up,
:root .increase,
:root .metric-change.positive,
:root .performance-positive,
:root .portfolio-gain,
:root .return-positive {
    color: var(--positive-color) !important;
}

:root .negative,
:root .negative-value,
:root .red,
:root .loss,
:root .down,
:root .decrease,
:root .metric-change.negative,
:root .performance-negative,
:root .portfolio-loss,
:root .return-negative {
    color: var(--negative-color) !important;
}

:root .warning,
:root .neutral,
:root .metric-change.neutral {
    color: var(--highlight-secondary) !important;
}

[data-theme="dark"] .info {
    color: var(--highlight-color) !important;
}

/* Keep white text on colored backgrounds */
[data-theme="dark"] button:not(.secondary-link):not(.outline),
[data-theme="dark"] .btn:not(.btn-outline):not(.btn-secondary),
[data-theme="dark"] .badge,
[data-theme="dark"] .tag,
[data-theme="dark"] .chip,
[data-theme="dark"] th,
[data-theme="dark"] .table-header,
[data-theme="dark"] .metric-change.positive,
[data-theme="dark"] .metric-change.negative,
[data-theme="dark"] .ticker-badge,
[data-theme="dark"] .category-icon,
[data-theme="dark"] .verdict-icon,
[data-theme="dark"] .card-icon {
    color: #ffffff !important;
}

/* SVG elements */
[data-theme="dark"] svg text {
    fill: var(--text-color) !important;
}

[data-theme="dark"] svg .keep-white,
[data-theme="dark"] svg .d3-slice-label,
[data-theme="dark"] svg #ratingText {
    fill: #ffffff !important;
}

/* === END ULTRA-AGGRESSIVE CONTRAST FIX === */

/* Enhanced D3.js Chart Animations */
@keyframes chartReveal {
    0% {
        opacity: 0;
        transform: scale(0.8) rotate(-10deg);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05) rotate(2deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

@keyframes pulseGlow {
    0%,
    100% {
        filter: drop-shadow(
            0 0 5px rgba(var(--highlight-color-rgb, 0, 123, 255), 0.3)
        );
    }
    50% {
        filter: drop-shadow(
            0 0 15px rgba(var(--highlight-color-rgb, 0, 123, 255), 0.6)
        );
    }
}

/* Enhanced rating chart styles */
.enhanced-rating-charts-container .overall-chart-wrapper svg {
    animation: chartReveal 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.enhanced-rating-charts-container .component-chart-wrapper svg {
    animation: chartReveal 1s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.enhanced-rating-charts-container
    .overall-rating-section:hover
    .overall-chart-wrapper
    svg {
    animation: pulseGlow 2s ease-in-out infinite;
}

/* Responsive adjustments for rating charts */
@media (max-width: 768px) {
    .overall-rating-section {
        flex-direction: column;
        gap: 20px;
    }

    .component-ratings-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .message-rating-charts {
        flex-direction: column;
        align-items: center;
    }

    .rating-chart-container-message {
        margin: 5px 0;
    }

    .enhanced-rating-charts-container {
        padding: 15px;
        margin: 15px 0;
    }
}

@media (max-width: 480px) {
    .rating-visualization-container {
        padding: 15px;
        margin: 10px 0;
    }

    .overall-rating-details .rating-score-text {
        font-size: 2.5rem;
    }

    .component-ratings-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* ===== COMPREHENSIVE FINANCIAL METRICS STYLES ===== */

.comprehensive-metrics-card {
    background: var(--card-bg-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 0;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.comprehensive-metrics-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.comprehensive-metrics-header {
    background: linear-gradient(135deg, var(--highlight-color), var(--accent-blue));
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comprehensive-metrics-header .header-content h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.comprehensive-metrics-header .header-subtitle {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.comprehensive-metrics-header .metrics-controls {
    display: flex;
    gap: 0.5rem;
}

.comprehensive-metrics-header .control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.comprehensive-metrics-header .control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.comprehensive-metrics-content {
    padding: 2rem;
}

.metrics-loader, .metrics-error {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-color);
}

.metrics-loader i {
    font-size: 2rem;
    color: var(--highlight-color);
    margin-bottom: 1rem;
    display: block;
}

.metrics-error i {
    font-size: 2rem;
    color: var(--negative-color);
    margin-bottom: 1rem;
    display: block;
}

.retry-btn {
    background: var(--highlight-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.2s ease;
}

.retry-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.comprehensive-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.metric-category {
    background: var(--card-bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.metric-category:hover {
    border-color: var(--highlight-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-category h4 {
    margin: 0 0 1rem 0;
    color: var(--highlight-color);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

.metric-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.metric-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.75rem;
    background: var(--bg-light);
    border-radius: 6px;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.metric-item:hover {
    background: var(--hover-bg);
    border-left-color: var(--highlight-color);
    transform: translateX(2px);
}

.metric-label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.metric-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--highlight-color);
}

.metric-interpretation {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-style: italic;
}

.comprehensive-metrics-summary {
    background: linear-gradient(135deg, var(--bg-light), var(--hover-bg));
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.comprehensive-metrics-summary h4 {
    margin: 0 0 1rem 0;
    color: var(--highlight-color);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.summary-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
    padding: 1rem;
    background: var(--card-bg-color);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.summary-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 500;
}

.summary-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--highlight-color);
}

/* Responsive Design for Comprehensive Metrics */
@media (max-width: 768px) {
    .comprehensive-metrics-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .comprehensive-metrics-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .summary-content {
        grid-template-columns: 1fr;
    }

    .comprehensive-metrics-content {
        padding: 1rem;
    }
}

/* Dark Mode Support for Comprehensive Metrics */
[data-theme="dark"] .comprehensive-metrics-card {
    background: var(--card-bg-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .metric-category {
    background: var(--card-bg-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .metric-item {
    background: var(--bg-dark);
}

[data-theme="dark"] .metric-item:hover {
    background: var(--hover-bg-dark);
}

[data-theme="dark"] .comprehensive-metrics-summary {
    background: var(--bg-dark);
    border-color: var(--border-color);
}

[data-theme="dark"] .summary-item {
    background: var(--card-bg-color);
    border-color: var(--border-color);
}
