/* static/enhanced_rating_charts.css */
/* Enhanced Rating Charts Styles with Modern Design */

:root {
    /* Enhanced Color Palette */
    --rating-excellent: #10b981;
    --rating-good: #3b82f6;
    --rating-average: #f59e0b;
    --rating-poor: #ef4444;
    --rating-neutral: #6b7280;

    /* Background Colors */
    --rating-bg-primary: var(--card-bg-color);
    --rating-bg-secondary: #f9fafb;
    --rating-bg-accent: #f3f4f6;

    /* Text Colors */
    --rating-text-primary: #111827;
    --rating-text-secondary: #6b7280;
    --rating-text-accent: #374151;

    /* Border Colors */
    --rating-border-light: #e5e7eb;
    --rating-border-medium: #d1d5db;
    --rating-border-dark: #9ca3af;

    /* Shadow Values */
    --rating-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --rating-shadow-md:
        0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --rating-shadow-lg:
        0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Typography */
    --rating-font-family:
        "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        sans-serif;
    --rating-font-size-xs: 0.75rem;
    --rating-font-size-sm: 0.875rem;
    --rating-font-size-base: 1rem;
    --rating-font-size-lg: 1.125rem;
    --rating-font-size-xl: 1.25rem;
    --rating-font-size-2xl: 1.5rem;

    /* Spacing */
    --rating-space-xs: 0.25rem;
    --rating-space-sm: 0.5rem;
    --rating-space-md: 1rem;
    --rating-space-lg: 1.5rem;
    --rating-space-xl: 2rem;
    --rating-space-2xl: 3rem;

    /* Border Radius */
    --rating-radius-sm: 0.375rem;
    --rating-radius-md: 0.5rem;
    --rating-radius-lg: 0.75rem;
    --rating-radius-xl: 1rem;

    /* Animation Timing */
    --rating-transition-fast: 150ms ease-in-out;
    --rating-transition-normal: 300ms ease-in-out;
    --rating-transition-slow: 500ms ease-in-out;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --rating-bg-primary: #1f2937;
    --rating-bg-secondary: #111827;
    --rating-bg-accent: #374151;
    --rating-text-primary: #f9fafb;
    --rating-text-secondary: #d1d5db;
    --rating-text-accent: #e5e7eb;
    --rating-border-light: #374151;
    --rating-border-medium: #4b5563;
    --rating-border-dark: #6b7280;
}

/* Enhanced Rating Visualization Container */
.enhanced-rating-visualization {
    background: var(--rating-bg-primary);
    border: 1px solid var(--rating-border-light);
    border-radius: var(--rating-radius-xl);
    padding: var(--rating-space-xl);
    margin: var(--rating-space-lg) 0;
    box-shadow: var(--rating-shadow-md);
    transition: all var(--rating-transition-normal);
    position: relative;
    overflow: hidden;
}

.enhanced-rating-visualization::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(
        90deg,
        var(--rating-excellent),
        var(--rating-good),
        var(--rating-average)
    );
    border-radius: var(--rating-radius-xl) var(--rating-radius-xl) 0 0;
}

.enhanced-rating-visualization:hover {
    transform: translateY(-2px);
    box-shadow: var(--rating-shadow-lg);
}

/* Visualization Title */
.visualization-title {
    font-family: var(--rating-font-family);
    font-size: var(--rating-font-size-2xl);
    font-weight: 700;
    color: var(--rating-text-primary);
    text-align: center;
    margin: 0 0 var(--rating-space-xl) 0;
    position: relative;
}

.visualization-title::after {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(
        90deg,
        var(--rating-excellent),
        var(--rating-good)
    );
    border-radius: var(--rating-radius-sm);
}

/* Chart Wrapper */
.enhanced-rating-chart-wrapper {
    width: 100%;
    position: relative;
    margin: var(--rating-space-lg) 0;
}

/* Chart SVG Styles */
.enhanced-rating-chart {
    width: 100%;
    height: auto;
    font-family: var(--rating-font-family);
    overflow: visible;
}

/* Rating Bar Groups */
.rating-bar-group {
    cursor: pointer;
    transition: all var(--rating-transition-fast);
}

.rating-bar-group:hover {
    filter: brightness(1.05);
}

/* Rating Labels */
.rating-label {
    font-size: var(--rating-font-size-sm);
    font-weight: 600;
    fill: var(--rating-text-primary);
    transition: all var(--rating-transition-fast);
}

.rating-bar-group:hover .rating-label {
    font-weight: 700;
    fill: var(--rating-text-accent);
}

/* Rating Values */
.rating-value {
    font-size: var(--rating-font-size-sm);
    font-weight: 700;
    transition: all var(--rating-transition-fast);
}

.rating-bar-group:hover .rating-value {
    font-size: var(--rating-font-size-base);
}

/* Background Bars */
.rating-bg-bar {
    fill: var(--rating-bg-accent);
    stroke: var(--rating-border-light);
    stroke-width: 1;
    transition: all var(--rating-transition-fast);
}

.rating-bar-group:hover .rating-bg-bar {
    stroke: var(--rating-border-medium);
}

/* Filled Bars */
.rating-filled-bar {
    transition: all var(--rating-transition-fast);
}

.rating-bar-group:hover .rating-filled-bar {
    filter: brightness(1.1) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Enhanced Tooltip */
.enhanced-rating-tooltip {
    position: absolute;
    visibility: hidden;
    background: rgba(17, 24, 39, 0.95);
    color: #ffffff;
    padding: var(--rating-space-sm) var(--rating-space-md);
    border-radius: var(--rating-radius-md);
    font-size: var(--rating-font-size-xs);
    font-weight: 500;
    font-family: var(--rating-font-family);
    pointer-events: none;
    z-index: 1000;
    box-shadow: var(--rating-shadow-lg);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 200px;
    word-wrap: break-word;
}

.enhanced-rating-tooltip::before {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(17, 24, 39, 0.95);
}

/* Text Commentary Container */
.commentary-container {
    margin-top: var(--rating-space-2xl);
    padding-top: var(--rating-space-xl);
    border-top: 2px solid var(--rating-border-light);
    position: relative;
}

.commentary-container::before {
    content: "";
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(
        90deg,
        var(--rating-good),
        var(--rating-excellent)
    );
}

/* Enhanced Text Commentary */
.enhanced-text-commentary {
    max-width: 800px;
    margin: 0 auto;
    font-family: var(--rating-font-family);
    line-height: 1.7;
    color: var(--rating-text-primary);
}

.commentary-paragraph {
    font-size: var(--rating-font-size-base);
    margin-bottom: var(--rating-space-lg);
    padding: var(--rating-space-md);
    background: var(--rating-bg-secondary);
    border-radius: var(--rating-radius-lg);
    border-left: 4px solid var(--rating-good);
    transition: all var(--rating-transition-normal);
    position: relative;
}

.commentary-paragraph:hover {
    background: var(--rating-bg-accent);
    transform: translateX(4px);
    box-shadow: var(--rating-shadow-sm);
}

.commentary-paragraph:nth-child(even) {
    border-left-color: var(--rating-excellent);
}

.commentary-paragraph:nth-child(3n) {
    border-left-color: var(--rating-average);
}

/* Strong and emphasis within commentary */
.commentary-paragraph strong {
    color: var(--rating-text-accent);
    font-weight: 700;
}

.commentary-paragraph em {
    color: var(--rating-good);
    font-style: italic;
}

/* Charts Container */
.charts-container {
    width: 100%;
    position: relative;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-rating-visualization {
        padding: var(--rating-space-lg);
        margin: var(--rating-space-md) 0;
    }

    .visualization-title {
        font-size: var(--rating-font-size-xl);
        margin-bottom: var(--rating-space-lg);
    }

    .enhanced-rating-chart-wrapper {
        margin: var(--rating-space-md) 0;
    }

    .rating-label {
        font-size: var(--rating-font-size-xs);
    }

    .rating-value {
        font-size: var(--rating-font-size-xs);
    }

    .commentary-container {
        margin-top: var(--rating-space-xl);
        padding-top: var(--rating-space-lg);
    }

    .commentary-paragraph {
        font-size: var(--rating-font-size-sm);
        padding: var(--rating-space-sm);
        margin-bottom: var(--rating-space-md);
    }
}

@media (max-width: 480px) {
    .enhanced-rating-visualization {
        padding: var(--rating-space-md);
        border-radius: var(--rating-radius-lg);
    }

    .visualization-title {
        font-size: var(--rating-font-size-lg);
    }

    .enhanced-rating-tooltip {
        font-size: var(--rating-font-size-xs);
        padding: var(--rating-space-xs) var(--rating-space-sm);
        max-width: 150px;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animation Classes for JavaScript Control */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slide-in-right {
    animation: slideInRight 0.4s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
}

/* Loading States */
.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--rating-text-secondary);
    font-family: var(--rating-font-family);
}

.chart-loading::after {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid var(--rating-border-light);
    border-top-color: var(--rating-good);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: var(--rating-space-sm);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .enhanced-rating-visualization,
    .rating-bar-group,
    .rating-filled-bar,
    .commentary-paragraph {
        transition: none;
    }

    .animate-fade-in-up,
    .animate-slide-in-right,
    .animate-scale-in {
        animation: none;
    }
}

/* Focus States for Keyboard Navigation */
.rating-bar-group:focus-visible {
    outline: 2px solid var(--rating-good);
    outline-offset: 2px;
    border-radius: var(--rating-radius-sm);
}

.commentary-paragraph:focus-visible {
    outline: 2px solid var(--rating-good);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .enhanced-rating-visualization {
        border: 2px solid var(--rating-text-primary);
    }

    .rating-bg-bar {
        stroke-width: 2;
    }

    .rating-filled-bar {
        stroke: var(--rating-text-primary);
        stroke-width: 1;
    }
}

/* Integration Styles - Prevent Conflicts */
.message.bot .enhanced-rating-container {
    margin: 20px 0;
    padding: 0;
    background: transparent;
    border: none;
    box-shadow: none;
}

.message.bot .enhanced-rating-visualization {
    margin: 15px 0;
    background: var(--rating-bg-primary);
    border: 1px solid var(--rating-border-light);
}

/* Override conflicting chatbot styles */
.enhanced-rating-container .rating-charts,
.enhanced-rating-container .component-ratings-grid,
.enhanced-rating-container .overall-rating-section {
    display: none !important;
}

/* Ensure proper stacking order */
.enhanced-rating-visualization {
    position: relative;
    z-index: 1;
}

.enhanced-rating-tooltip {
    z-index: 9999;
}

/* Message content integration */
.message-content .enhanced-rating-container {
    width: 100%;
    max-width: none;
}

/* Chatbot message specific adjustments */
#chatbot-messages .enhanced-rating-visualization {
    max-width: 100%;
    margin: 10px 0;
    font-size: 14px;
}

#chatbot-messages .visualization-title {
    font-size: 18px;
    margin-bottom: 15px;
}

#chatbot-messages .rating-label {
    font-size: 12px;
}

#chatbot-messages .rating-value {
    font-size: 12px;
}

/* Animation trigger fixes */
.enhanced-rating-chart-wrapper {
    opacity: 1;
    transform: none;
}

.enhanced-rating-chart-wrapper.animate-in {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Responsive adjustments for chatbot */
@media (max-width: 768px) {
    #chatbot-messages .enhanced-rating-visualization {
        padding: 15px;
        margin: 8px 0;
    }

    #chatbot-messages .visualization-title {
        font-size: 16px;
    }

    #chatbot-messages .commentary-paragraph {
        font-size: 13px;
        padding: 8px;
    }
}

/* Force refresh animation */
.enhanced-rating-chart.force-animate .rating-filled-bar {
    animation: barGrowth 1.2s ease-out forwards;
}

@keyframes barGrowth {
    from {
        width: 0;
    }
    to {
        width: var(--target-width, 100%);
    }
}

/* Text content animation fixes */
.enhanced-text-commentary.animate-text .commentary-paragraph {
    animation: slideInUp 0.8s ease-out forwards;
    animation-fill-mode: both;
}

.enhanced-text-commentary.animate-text .commentary-paragraph:nth-child(1) {
    animation-delay: 0ms;
}

.enhanced-text-commentary.animate-text .commentary-paragraph:nth-child(2) {
    animation-delay: 150ms;
}

.enhanced-text-commentary.animate-text .commentary-paragraph:nth-child(3) {
    animation-delay: 300ms;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll animation helpers */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.scroll-animate.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Print Styles */
@media print {
    .enhanced-rating-visualization {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .enhanced-rating-tooltip {
        display: none;
    }

    .commentary-paragraph {
        background: transparent;
        border: 1px solid #ccc;
    }
}

/* Horizontal bar chart alignment fixes */
.horizontal-bar-container {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.horizontal-bar-label {
    margin-bottom: 5px;
    line-height: 1.5;
    font-weight: 500;
}

.horizontal-bar-wrapper {
    height: 24px;
    position: relative;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    overflow: hidden;
}

.horizontal-bar {
    position: relative;
    height: 24px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    transition: width 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.horizontal-bar-value {
    position: absolute;
    right: 10px;
    top: 50% !important;
    transform: translateY(-50%) !important;
    line-height: 1;
    margin: 0;
    padding: 0;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
}

/* Specific fix for the valuation bar */
.horizontal-bar-container:nth-child(4) .horizontal-bar-value {
    top: 50% !important;
    transform: translateY(-50%) !important;
}

/* Rating visualization container */
.rating-visualization {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 15px 0;
}

.rating-title {
    margin: 0 0 20px 0;
    color: var(--text-color);
    font-size: 1.2em;
    font-weight: 600;
    text-align: center;
}

/* Fix for component rating cards */
.component-rating-item {
    position: relative;
    padding-bottom: 60px;
}

.component-score-display {
    position: absolute;
    bottom: 24px;
    left: 0;
    right: 0;
    text-align: center;
    margin: 0;
    padding: 0;
    line-height: 1;
}

/* Dark theme adjustments - Use theme variables from base.html */
[data-theme="dark"] .rating-visualization {
    background: var(--card-bg-color);
    box-shadow: var(--shadow-lg);
    color: var(--text-color);
}

[data-theme="dark"] .rating-title {
    color: var(--text-color);
}

[data-theme="dark"] .horizontal-bar-label {
    color: var(--text-muted-color);
}

[data-theme="dark"] .horizontal-bar-wrapper {
    background-color: var(--nav-hover-color);
}

/* Enhanced dark mode support for rating charts */
[data-theme="dark"] {
    --rating-bg-primary: var(--card-bg-color);
    --rating-bg-secondary: var(--nav-hover-color);
    --rating-bg-accent: var(--input-bg-color);
    --rating-text-primary: var(--text-color);
    --rating-text-secondary: var(--text-muted-color);
    --rating-text-accent: var(--text-color);
    --rating-border-light: var(--border-color);
    --rating-border-medium: var(--border-color);
    --rating-border-dark: var(--border-color);
}

/* === COMPREHENSIVE CONTRAST FIX FOR RATING CHARTS === */
/* Fix all remaining hardcoded colors */

/* Fix tooltip backgrounds */
.rating-tooltip {
    background: var(--card-bg-color) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

.rating-tooltip::after {
    border-top-color: var(--card-bg-color) !important;
}

/* Fix progress bar backgrounds */
.horizontal-bar-wrapper {
    background-color: var(--nav-hover-color) !important;
}

[data-theme="dark"] .horizontal-bar-wrapper {
    background-color: var(--nav-hover-color) !important;
}

/* Fix loading spinner colors */
.rating-loading-spinner {
    border-top-color: var(--highlight-color) !important;
    color: var(--text-muted-color) !important;
}

/* Fix all text elements */
.rating-visualization h3,
.rating-visualization h4,
.rating-visualization p,
.rating-visualization span {
    color: var(--text-color) !important;
}

/* Keep white text on colored progress bars */
.horizontal-bar-fill {
    color: #ffffff !important;
}

/* === END COMPREHENSIVE CONTRAST FIX === */

/* Enhanced component rating item alignment */
.component-rating-item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 24px;
}

/* Ensure all component icons are the same size and position */
.component-icon {
    font-size: 2rem;
    margin-bottom: 8px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    line-height: 1;
}

/* Fix for component ratings grid to ensure equal heights */
.component-ratings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 30px;
    align-items: stretch;
}

/* Ensure all rating cards have the same height structure */
.component-rating-item {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.95),
        rgba(255, 255, 255, 0.8)
    );
    padding: 24px;
    border-radius: 16px;
    text-align: center;
    border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.15);
    backdrop-filter: blur(15px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 4px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: scale(0.7) translateY(40px) rotateX(20deg) rotateY(5deg);
    perspective: 1200px;
    transform-style: preserve-3d;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

/* Consistent spacing for component chart wrapper */
.component-chart-wrapper {
    margin: 16px 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Ensure component score displays are positioned consistently */
.component-score-display {
    font-size: 1.4rem;
    font-weight: 700;
    margin-top: auto;
    padding-top: 12px;
    background: transparent;
    border: none;
    display: flex;
    align-items: baseline;
    justify-content: center;
    width: 100%;
}

/* More specific fix for the Valuation rating card */
.component-ratings-grid .component-rating-item:nth-child(3) {
    display: flex;
    flex-direction: column;
}

/* Force equal heights for all rating cards */
.component-ratings-grid .component-rating-item {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Ensure all percentage displays are at the same vertical position */
.component-rating-item .component-score-display {
    position: absolute;
    bottom: 24px;
    left: 0;
    right: 0;
    text-align: center;
}

/* Make chart wrappers the same height */
.component-rating-item .component-chart-wrapper {
    height: 90px;
    margin-bottom: 40px;
}

/* Ensure consistent vertical spacing */
.component-rating-item .component-header {
    margin-bottom: 15px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Ensure all cards have the same internal structure */
.component-ratings-grid .component-rating-item {
    padding-bottom: 60px;
    position: relative;
}

/* Valuation card specific fixes */
.component-ratings-grid .component-rating-item:nth-child(3) .component-score-display {
    position: absolute !important;
    bottom: 24px !important;
    left: 0 !important;
    right: 0 !important;
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: baseline !important;
}

/* Ensure all component rating items have the same structure */
.component-rating-item {
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
    padding-bottom: 60px !important;
    height: 100% !important;
}

/* Ensure all score displays have the same positioning */
.component-score-display {
    position: absolute !important;
    bottom: 24px !important;
    left: 0 !important;
    right: 0 !important;
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
}

/* Ensure all chart wrappers have the same height */
.component-chart-wrapper {
    height: 90px !important;
    margin-bottom: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Ensure all headers have the same height */
.component-header {
    height: 60px !important;
    margin-bottom: 15px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
}

/* Fix for horizontal bar charts */
.horizontal-bar-value {
    position: absolute !important;
    right: 10px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    line-height: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Precise positioning for all percentage displays */
.component-score-display {
    position: absolute !important;
    bottom: 24px !important;
    left: 0 !important;
    right: 0 !important;
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    height: 28px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: baseline !important;
}

/* Ensure all percentage spans have consistent styling */
.component-score-display span:last-child {
    font-size: 0.8em !important;
    margin-left: 1px !important;
    vertical-align: baseline !important;
}

/* Ensure all number spans have consistent styling */
.component-score-display span:first-child {
    vertical-align: baseline !important;
}

/* Force all cards to have the same height */
.component-rating-item {
    height: 100% !important;
    box-sizing: border-box !important;
}

/* Force all chart wrappers to have the same dimensions */
.component-chart-wrapper {
    height: 90px !important;
    margin-bottom: 40px !important;
    margin-top: 0 !important;
}

/* Force all SVG elements to be centered */
.component-chart-wrapper svg {
    display: block !important;
    margin: 0 auto !important;
}

/* Make all cards have identical internal structure */
.component-ratings-grid .component-rating-item {
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
    padding-bottom: 60px !important;
}

/* Ensure consistent header height across all cards */
.component-header {
    height: 60px !important;
    margin-bottom: 15px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
}
