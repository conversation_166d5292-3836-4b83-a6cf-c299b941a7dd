#!/usr/bin/env python3
"""
Test the currency update endpoint to ensure portfolio entries are updated correctly
"""

import sys
sys.path.append('.')

def test_currency_update_simulation():
    """Simulate the currency update process"""
    
    print("=== CURRENCY UPDATE ENDPOINT SIMULATION ===")
    
    # Simulate existing portfolio with USD entries
    portfolio_data = [
        {
            'ticker': 'GOOGL',
            'shares': 11.96,
            'amount_invested': 1838.36,  # This is the corrupted number from screenshot
            'buy_price': 153.64,
            'currency': 'USD'
        },
        {
            'ticker': 'AMZN', 
            'shares': 11.98,
            'amount_invested': 2187.52,
            'buy_price': 182.63,
            'currency': 'USD'
        }
    ]
    
    print("📊 Original Portfolio (USD):")
    for entry in portfolio_data:
        print(f"   {entry['ticker']}: ${entry['amount_invested']:.2f} invested, {entry['shares']} shares @ ${entry['buy_price']:.2f}")
    
    # Simulate currency conversion to DKK
    new_currency = 'DKK'
    current_currency = 'USD'
    
    # Currency rates (same as in app.py)
    currency_rates = {
        'USD': 1.0, 'DKK': 0.145
    }
    
    def convert_to_usd(amount, from_currency):
        if from_currency == 'USD':
            return amount
        rate = currency_rates.get(from_currency, 1.0)
        return amount * rate
    
    print(f"\n🔄 Converting from {current_currency} to {new_currency}...")
    
    # Update all entries
    for entry in portfolio_data:
        entry_currency = entry.get('currency', current_currency)
        
        if entry_currency != new_currency:
            # Convert amounts
            current_amount = entry.get('amount_invested', 0)
            current_price = entry.get('buy_price', 0)
            
            # Convert via USD
            amount_usd = convert_to_usd(current_amount, entry_currency)
            price_usd = convert_to_usd(current_price, entry_currency)
            
            # Convert to new currency
            new_rate = currency_rates.get(new_currency, 1.0)
            new_amount = amount_usd / new_rate if new_rate != 0 else amount_usd
            new_price = price_usd / new_rate if new_rate != 0 else price_usd
            
            print(f"   {entry['ticker']}: ${current_amount:.2f} USD → {new_amount:.2f} {new_currency}")
            print(f"      Price: ${current_price:.2f} USD → {new_price:.2f} {new_currency}")
            
            # Update entry
            entry['amount_invested'] = new_amount
            entry['buy_price'] = new_price
            entry['currency'] = new_currency
    
    print(f"\n📊 Updated Portfolio ({new_currency}):")
    for entry in portfolio_data:
        currency_symbol = 'kr' if entry['currency'] == 'DKK' else '$'
        if entry['currency'] == 'DKK':
            print(f"   {entry['ticker']}: {entry['amount_invested']:.2f} {currency_symbol} invested, {entry['shares']} shares @ {entry['buy_price']:.2f} {currency_symbol}")
        else:
            print(f"   {entry['ticker']}: {currency_symbol}{entry['amount_invested']:.2f} invested, {entry['shares']} shares @ {currency_symbol}{entry['buy_price']:.2f}")
    
    # Check if the numbers look reasonable
    print(f"\n✅ Validation:")
    for entry in portfolio_data:
        amount = entry['amount_invested']
        price = entry['buy_price']
        
        if entry['currency'] == 'DKK':
            # DKK amounts should be ~6.9x larger than USD
            if 10000 <= amount <= 20000:  # Reasonable range for DKK
                print(f"   ✅ {entry['ticker']} amount looks reasonable: {amount:.2f} kr")
            else:
                print(f"   ❌ {entry['ticker']} amount seems wrong: {amount:.2f} kr")
                
            if 500 <= price <= 2000:  # Reasonable range for stock price in DKK
                print(f"   ✅ {entry['ticker']} price looks reasonable: {price:.2f} kr")
            else:
                print(f"   ❌ {entry['ticker']} price seems wrong: {price:.2f} kr")

if __name__ == "__main__":
    test_currency_update_simulation()