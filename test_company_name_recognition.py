#!/usr/bin/env python3
"""
Test company name recognition and conversion to tickers
"""

import io
import csv
from portfolio_import import process_spreadsheet_upload, convert_company_name_to_ticker, is_valid_ticker_format

def test_company_name_conversion():
    """Test the company name to ticker conversion function"""
    print("🔍 Testing Company Name to Ticker Conversion")
    print("=" * 50)
    
    test_cases = [
        # Test cases that should work
        ('Apple', 'AAPL'),
        ('Apple Inc', 'AAPL'),
        ('apple inc.', 'AAPL'),
        ('Microsoft', 'MSFT'),
        ('Microsoft Corporation', 'MSFT'),
        ('Alphabet Inc.', 'GOOGL'),
        ('Google', 'GOOGL'),
        ('Amazon', 'AMZN'),
        ('Amazon.com Inc', 'AMZN'),
        ('Tesla', 'TSLA'),
        ('ASML Holding N.V.', 'ASML'),
        ('Uber Technologies Inc.', 'UBER'),
        ('a', 'A'),  # Single letter case
        
        # Test cases that are already tickers
        ('AAPL', 'AAPL'),
        ('GOOGL', 'GOOGL'),
        ('MSFT', 'MSFT'),
        
        # Test cases that should remain unchanged (unknown companies)
        ('Unknown Company', 'Unknown Company'),
        ('Some Random Business', 'Some Random Business'),
    ]
    
    all_passed = True
    
    for input_name, expected_ticker in test_cases:
        result = convert_company_name_to_ticker(input_name)
        
        if result == expected_ticker:
            print(f"✅ '{input_name}' -> '{result}'")
        else:
            print(f"❌ '{input_name}' -> '{result}' (expected '{expected_ticker}')")
            all_passed = False
    
    return all_passed

def test_ticker_validation():
    """Test the ticker validation function"""
    print(f"\n🔍 Testing Ticker Validation")
    print("=" * 50)
    
    test_cases = [
        # Valid tickers
        ('AAPL', True),
        ('GOOGL', True),
        ('MSFT', True),
        ('A', True),
        ('BRK.B', True),
        
        # Invalid tickers
        ('', False),
        ('TOOLONG', False),
        ('123', False),
        ('apple', False),  # lowercase
        ('Apple Inc', False),  # company name
    ]
    
    all_passed = True
    
    for ticker, expected_valid in test_cases:
        result = is_valid_ticker_format(ticker)
        
        if result == expected_valid:
            status = "Valid" if result else "Invalid"
            print(f"✅ '{ticker}' -> {status}")
        else:
            expected_status = "Valid" if expected_valid else "Invalid"
            actual_status = "Valid" if result else "Invalid"
            print(f"❌ '{ticker}' -> {actual_status} (expected {expected_status})")
            all_passed = False
    
    return all_passed

def test_spreadsheet_with_company_names():
    """Test spreadsheet processing with company names"""
    print(f"\n🔍 Testing Spreadsheet with Company Names")
    print("=" * 50)
    
    # Create test CSV with company names
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header and data with company names
    writer.writerow(['Company', 'Amount', 'Price', 'Date'])
    writer.writerow(['Apple Inc.', '1500.00', '150.50', '2023-04-15'])
    writer.writerow(['Microsoft Corporation', '2000.00', '250.75', '2023-03-20'])
    writer.writerow(['Alphabet Inc.', '1200.00', '120.25', '2023-05-10'])
    writer.writerow(['ASML Holding N.V.', '800.00', '400.00', '2023-06-01'])
    writer.writerow(['a', '100.00', '100.00', '2023-07-01'])  # Single letter case
    
    # Get the CSV content as bytes
    csv_content = output.getvalue().encode('utf-8')
    
    print("Test CSV content:")
    print(output.getvalue())
    
    # Process the spreadsheet
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_content, 'test_companies.csv', google_vision_api_key)
    
    print(f"\nResult:")
    print(f"Success: {result['success']}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Warnings: {result.get('warnings', [])}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    
    # Check the results
    portfolio = result.get('portfolio', [])
    expected_tickers = ['AAPL', 'MSFT', 'GOOGL', 'ASML', 'A']
    
    if portfolio:
        print(f"\nExtracted portfolio:")
        found_tickers = []
        for entry in portfolio:
            ticker = entry.get('ticker', 'UNKNOWN')
            amount = entry.get('amount_invested', 0)
            price = entry.get('buy_price', 0)
            found_tickers.append(ticker)
            print(f"  {ticker}: ${amount} @ ${price}")
        
        # Check if we got the expected tickers
        missing_tickers = [t for t in expected_tickers if t not in found_tickers]
        unexpected_tickers = [t for t in found_tickers if t not in expected_tickers]
        
        success = True
        
        if missing_tickers:
            print(f"\n❌ Missing expected tickers: {missing_tickers}")
            success = False
        
        if unexpected_tickers:
            print(f"\n⚠️  Unexpected tickers: {unexpected_tickers}")
        
        if not missing_tickers and not unexpected_tickers:
            print(f"\n✅ All expected tickers found correctly!")
        
        return success and result['success']
    else:
        print(f"\n❌ No portfolio entries found!")
        return False

def test_error_case_with_invalid_company():
    """Test what happens with invalid/unknown company names"""
    print(f"\n🔍 Testing Invalid Company Names")
    print("=" * 50)
    
    # Create test CSV with invalid company names
    output = io.StringIO()
    writer = csv.writer(output)
    
    writer.writerow(['Company', 'Amount', 'Price', 'Date'])
    writer.writerow(['Unknown Company XYZ', '1000.00', '100.00', '2023-04-15'])
    writer.writerow(['Random Business 123', '500.00', '50.00', '2023-03-20'])
    
    csv_content = output.getvalue().encode('utf-8')
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_content, 'test_invalid.csv', google_vision_api_key)
    
    print(f"Result for invalid companies:")
    print(f"Success: {result['success']}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    
    # Should have helpful error messages
    errors = result.get('errors', [])
    has_helpful_errors = any('Unknown company name' in error or 'ticker symbol' in error for error in errors)
    
    if has_helpful_errors:
        print("✅ Helpful error messages provided for unknown companies")
        return True
    else:
        print("❌ No helpful error messages for unknown companies")
        return False

def main():
    print("🚀 COMPANY NAME RECOGNITION TEST")
    print("=" * 60)
    print("Testing the enhanced portfolio import with company name support")
    print()
    
    test1 = test_company_name_conversion()
    test2 = test_ticker_validation()
    test3 = test_spreadsheet_with_company_names()
    test4 = test_error_case_with_invalid_company()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    
    if test1 and test2 and test3 and test4:
        print("🎉 SUCCESS! Company name recognition is working!")
        print()
        print("✅ Company names correctly converted to tickers")
        print("✅ Ticker validation working properly")
        print("✅ Spreadsheet processing handles company names")
        print("✅ Helpful error messages for unknown companies")
        print()
        print("🎯 Users can now input company names like:")
        print("   • 'Apple Inc.' -> AAPL")
        print("   • 'Microsoft Corporation' -> MSFT")
        print("   • 'Alphabet Inc.' -> GOOGL")
        print("   • 'ASML Holding N.V.' -> ASML")
        print("   • Single letter 'a' -> A")
        
    else:
        print("❌ Some tests failed!")
        if not test1:
            print("   - Company name conversion issues")
        if not test2:
            print("   - Ticker validation issues")
        if not test3:
            print("   - Spreadsheet processing issues")
        if not test4:
            print("   - Error handling issues")

if __name__ == "__main__":
    main()
