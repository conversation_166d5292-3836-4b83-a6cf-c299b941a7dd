#!/usr/bin/env python3
"""
Test script for the enhanced portfolio import system.
This script tests the improved extraction capabilities.
"""

import sys
import os

# Add the current directory to the path so we can import portfolio_import
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import AIPortfolioExtractor, process_image_upload

def test_portfolio_extraction():
    """Test the portfolio extraction with sample text data."""
    
    # Sample text that might come from OCR of a portfolio image
    sample_texts = [
        # Test case 1: Simple format with VS ticker
        """
        VS
        5.00 shares
        $12.00 buy price
        $60.00 total invested
        """,
        
        # Test case 2: Danish format
        """
        Versus Systems Inc.
        NasdaqGS:VS
        Antal: 13 stk
        GAK: 161,61 USD
        Markedsværdi: 2.462,85 USD
        """,
        
        # Test case 3: Multiple stocks
        """
        AAPL
        10 shares @ $150.00
        Total: $1,500.00
        
        GOOGL
        5 shares @ $2,800.00
        Total: $14,000.00
        
        TSLA
        20 shares @ $250.00
        Total: $5,000.00
        """,
        
        # Test case 4: Table format
        """
        Ticker  Shares  Price   Total
        MSFT    15      $300    $4,500
        AMZN    8       $3,200  $25,600
        META    25      $320    $8,000
        """,
        
        # Test case 5: Mixed format with OCR errors
        """
        G00GL (Alphabet Inc.)
        Shares: 3
        Avg Cost: $2,750.50
        Market Value: $8,251.50
        
        V5 (Versus Systems)
        Shares: 100
        Avg Cost: $12.50
        Current Value: $1,250.00
        """
    ]
    
    # Initialize the AI extractor
    google_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"  # Your Google Vision API key
    eodhd_api_key = "673b0b8b8b8b8b.12345678"  # Default EODHD key
    
    extractor = AIPortfolioExtractor(google_api_key, eodhd_api_key)
    
    print("=== TESTING ENHANCED PORTFOLIO EXTRACTION ===\n")
    
    for i, sample_text in enumerate(sample_texts, 1):
        print(f"--- TEST CASE {i} ---")
        print(f"Input text:\n{sample_text}")
        print("\n" + "="*50)
        
        try:
            # Test the AI extraction
            result = extractor.extract_portfolio_data_with_ai(sample_text)
            
            print(f"Extraction successful: {result['success']}")
            print(f"Number of entries found: {len(result.get('portfolio', []))}")
            print(f"Cash position: ${result.get('cash_position', 0):.2f}")
            
            if result.get('portfolio'):
                print("\nExtracted entries:")
                for j, entry in enumerate(result['portfolio'], 1):
                    print(f"  {j}. {entry.get('ticker', 'N/A')}")
                    print(f"     Shares: {entry.get('shares', 'N/A')}")
                    print(f"     Buy Price: ${entry.get('buy_price', 0):.2f}")
                    print(f"     Amount Invested: ${entry.get('amount_invested', 0):.2f}")
                    print(f"     Current Value: ${entry.get('current_value', 0):.2f}")
                    print(f"     Purchase Date: {entry.get('purchase_date', 'N/A')}")
            
            if result.get('warnings'):
                print(f"\nWarnings: {result['warnings']}")
            
            if result.get('errors'):
                print(f"\nErrors: {result['errors']}")
                
        except Exception as e:
            print(f"ERROR: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "="*80 + "\n")

def test_specific_vs_case():
    """Test the specific VS ticker case that was problematic."""
    
    print("=== TESTING SPECIFIC VS CASE ===\n")
    
    # This simulates the text that might be extracted from your image
    vs_text = """
    VS
    5 shares
    $12.00 per share
    $60.00 total
    """
    
    google_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.12345678"
    
    extractor = AIPortfolioExtractor(google_api_key, eodhd_api_key)
    
    print(f"Testing with text: {repr(vs_text)}")
    
    result = extractor.extract_portfolio_data_with_ai(vs_text)
    
    print(f"Success: {result['success']}")
    print(f"Entries found: {len(result.get('portfolio', []))}")
    
    if result.get('portfolio'):
        for entry in result['portfolio']:
            print(f"Ticker: {entry.get('ticker')}")
            print(f"Shares: {entry.get('shares')}")
            print(f"Buy Price: ${entry.get('buy_price', 0):.2f}")
            print(f"Amount Invested: ${entry.get('amount_invested', 0):.2f}")
    
    # Also test the Danish parser specifically
    print("\n--- Testing Danish Parser Directly ---")
    danish_result = extractor._parse_danish_portfolio_format(vs_text)
    if danish_result:
        print(f"Danish parser found: {danish_result}")
    else:
        print("Danish parser found nothing")

if __name__ == "__main__":
    print("Portfolio Import Enhancement Test")
    print("=" * 50)
    
    # Test the general extraction
    test_portfolio_extraction()
    
    # Test the specific VS case
    test_specific_vs_case()
    
    print("\nTest completed!")
