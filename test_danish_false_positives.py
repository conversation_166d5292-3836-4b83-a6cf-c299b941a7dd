#!/usr/bin/env python3
"""
Test the Danish false positive filtering
"""

from portfolio_import import process_image_upload, PortfolioImportService

def test_danish_false_positives():
    """Test with Danish text that should NOT extract false tickers"""
    print("🔍 Testing Danish False Positive Filtering")
    print("=" * 60)
    
    # This simulates what OCR extracts from the user's Danish Google Finance image
    # Should only extract GOOGL, not STK, JUL, NSKAB
    simulated_ocr_text = """
    GOOGL
    
    Mine beholdninger
    Antal
    13 stk
    
    GAK
    161,61 USD
    
    Markedsværdi
    2.462,85 USD
    
    Ureal.afkast
    +9,9%
    
    Vigtige datoer
    Dage til regnskab
    1
    
    Næste regnskab
    23. jul.
    
    Om virksomheden
    Alphabet is a holding company that wholly owns
    internet giant Google. The California-based
    company derives slightly less ... Læs mere
    
    Nøgletal
    """
    
    print("Simulated OCR text from Danish Google Finance:")
    print(simulated_ocr_text)
    print("\n" + "=" * 60)
    
    # Test with the portfolio import service
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.********"
    
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    
    print("Processing with portfolio import service...")
    result = service.extract_portfolio_from_text(simulated_ocr_text)
    
    print(f"\nResults:")
    print(f"Success: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    if result.portfolio_entries:
        print(f"\nExtracted portfolio entries:")
        for i, entry in enumerate(result.portfolio_entries, 1):
            print(f"  {i}. {entry.ticker}")
            print(f"     Shares: {entry.shares}")
            print(f"     Buy Price: ${entry.buy_price:.2f}")
            print(f"     Amount Invested: ${entry.amount_invested:.2f}")
            print()
    
    # Check for false positives (these should NOT be extracted as tickers)
    false_positives = ['STK', 'JUL', 'NSKAB', 'GAK', 'USD', 'DAGE', 'NÆSTE', 'REGN', 'SKAB', 'ANTAL', 'RDI']
    found_false_positives = []
    
    for entry in result.portfolio_entries:
        if entry.ticker in false_positives:
            found_false_positives.append(entry.ticker)
    
    # Check for correct ticker
    found_googl = any(entry.ticker == 'GOOGL' for entry in result.portfolio_entries)
    
    success = True
    
    if found_false_positives:
        print(f"❌ FOUND FALSE POSITIVES: {found_false_positives}")
        success = False
    else:
        print("✅ NO FALSE POSITIVES FOUND")
    
    if found_googl:
        print("✅ CORRECTLY FOUND GOOGL")
    else:
        print("❌ FAILED TO FIND GOOGL")
        success = False
    
    if success:
        print("\n🎉 SUCCESS: Danish false positive filtering works correctly!")
        print("Only legitimate tickers are extracted, Danish words are filtered out.")
    else:
        print("\n❌ ISSUE: Danish false positive filtering needs improvement")
    
    return success

def test_context_validation():
    """Test the context validation method directly"""
    print("\n" + "=" * 60)
    print("🔍 Testing Context Validation Method")
    print("=" * 60)
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.********"
    
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    extractor = service.ai_extractor
    
    # Test cases: (ticker, context, should_be_valid)
    test_cases = [
        ('GOOGL', 'GOOGL stock price', True),  # Valid ticker
        ('STK', '13 stk shares', False),       # Danish "pieces"
        ('JUL', '23. jul. date', False),       # Danish "July"
        ('NSKAB', 'regnskab accounting', False), # Danish "accounting"
        ('AAPL', 'AAPL company', True),        # Valid ticker
        ('USD', 'price in USD', False),        # Currency
        ('DAGE', '1 dage til', False),         # Danish "days"
        ('GAK', 'GAK\n161,61 USD', False),    # Danish price label
        ('GAK', 'Current price GAK 161.61 USD', False), # Price label context
    ]
    
    print("Testing context validation:")
    all_passed = True
    
    for ticker, context, expected in test_cases:
        result = extractor._is_valid_ticker_with_context(ticker, context)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{ticker}' in '{context}' -> {result} (expected: {expected})")
        if result != expected:
            all_passed = False
    
    if all_passed:
        print("\n✅ All context validation tests passed!")
    else:
        print("\n❌ Some context validation tests failed!")
    
    return all_passed

if __name__ == "__main__":
    print("Testing Danish False Positive Filtering")
    print("=" * 60)
    
    # Test 1: Full extraction with Danish text
    test1_success = test_danish_false_positives()
    
    # Test 2: Context validation method
    test2_success = test_context_validation()
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS:")
    print("=" * 60)
    
    if test1_success and test2_success:
        print("🎉 SUCCESS: All tests passed!")
        print("✅ Danish false positive filtering works correctly")
        print("✅ Context validation works correctly")
        print("\n🎯 USER'S ISSUE IS RESOLVED!")
    else:
        print("❌ SOME TESTS FAILED:")
        if not test1_success:
            print("❌ Danish false positive filtering needs work")
        if not test2_success:
            print("❌ Context validation needs work")
        print("\n🔧 NEED TO FIX: False positive filtering")
