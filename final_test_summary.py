#!/usr/bin/env python3
"""
Final comprehensive test to confirm the portfolio import fix
"""

import io
import csv
from portfolio_import import process_spreadsheet_upload

def test_exact_user_issue():
    """Test the exact issue the user reported"""
    print("🔍 Testing Exact User Issue")
    print("=" * 40)
    print("User reported: 'it still only provides default data:'")
    print("AMZN $2,554.80 $425.80")
    print("NVDA $2,554.80 $425.80") 
    print("COM  $2,554.80 $425.80")
    print()
    
    # Create a realistic CSV that user might upload
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Test both formats that might cause issues
    test_cases = [
        {
            'name': 'Named Columns',
            'headers': ['Ticker', 'Amount Invested', 'Buy Price', 'Shares', 'Purchase Date'],
            'data': [
                ['AAPL', '5000.00', '175.25', '28.57', '2024-01-15'],
                ['MSFT', '3000.00', '380.50', '7.88', '2024-02-10'],
                ['GOOGL', '2500.00', '125.75', '19.88', '2024-03-05']
            ]
        },
        {
            'name': '<PERSON> Columns (A,B,C,D)',
            'headers': ['A', 'B', 'C', 'D'],
            'data': [
                ['TSLA', '4000.00', '200.00', '2024-01-20'],
                ['NVDA', '6000.00', '850.00', '2024-02-15'],
                ['AMD', '1500.00', '95.50', '2024-03-10']
            ]
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        print(f"\n📋 Testing {test_case['name']}:")
        
        # Create CSV
        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(test_case['headers'])
        for row in test_case['data']:
            writer.writerow(row)
        
        csv_content = output.getvalue().encode('utf-8')
        
        # Process
        google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
        result = process_spreadsheet_upload(csv_content, f'{test_case["name"].lower().replace(" ", "_")}.csv', google_vision_api_key)
        
        print(f"  Success: {result['success']}")
        print(f"  Entries: {len(result.get('portfolio', []))}")
        
        if result['success'] and result.get('portfolio'):
            # Check for the problematic default data
            has_default_data = False
            for entry in result['portfolio']:
                amount = entry.get('amount_invested', 0)
                price = entry.get('buy_price', 0)
                
                # Check for the exact default values user reported
                if amount == 2554.80 or price == 425.80:
                    has_default_data = True
                    print(f"  ❌ FOUND DEFAULT DATA: {entry['ticker']} ${amount} @ ${price}")
                    break
            
            if not has_default_data:
                print(f"  ✅ No default data detected!")
                print(f"  📊 Actual data:")
                for entry in result['portfolio'][:3]:  # Show first 3
                    print(f"     {entry['ticker']}: ${entry['amount_invested']} @ ${entry['buy_price']}")
            else:
                print(f"  ❌ PROBLEM: Still returning default data!")
                all_passed = False
        else:
            print(f"  ❌ FAILED: {result.get('errors', [])}")
            all_passed = False
    
    return all_passed

def test_api_format_consistency():
    """Test that the API format is consistent with what the web interface expects"""
    print("\n🔌 Testing API Format Consistency")
    print("=" * 40)
    
    # Create test data
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(['Ticker', 'Amount', 'Price', 'Date'])
    writer.writerow(['AAPL', '1000.00', '150.00', '2024-01-01'])
    
    csv_content = output.getvalue().encode('utf-8')
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_content, 'test.csv', google_vision_api_key)
    
    # Check API format
    required_keys = ['success', 'portfolio', 'cash_position', 'errors', 'warnings']
    has_all_keys = all(key in result for key in required_keys)
    
    print(f"  Has all required keys: {has_all_keys}")
    print(f"  Keys present: {list(result.keys())}")
    
    if result.get('portfolio'):
        entry = result['portfolio'][0]
        portfolio_keys = ['ticker', 'amount_invested', 'buy_price', 'purchase_date', 'shares']
        has_portfolio_keys = all(key in entry for key in portfolio_keys)
        print(f"  Portfolio entry has all keys: {has_portfolio_keys}")
        print(f"  Portfolio entry keys: {list(entry.keys())}")
        
        return has_all_keys and has_portfolio_keys
    
    return has_all_keys

def main():
    print("🚀 FINAL PORTFOLIO IMPORT FIX VERIFICATION")
    print("=" * 60)
    print("Testing the exact issue reported by the user:")
    print("'it still only provides default data: AMZN $2,554.80 $425.80'")
    print()
    
    test1 = test_exact_user_issue()
    test2 = test_api_format_consistency()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    print("=" * 60)
    
    if test1 and test2:
        print("🎉 SUCCESS! All tests passed!")
        print()
        print("✅ Portfolio import now processes REAL data correctly")
        print("✅ No more default/mock data (AMZN $2,554.80 $425.80)")
        print("✅ Works with named columns (Ticker, Amount, Price)")
        print("✅ Works with simple columns (A, B, C, D)")
        print("✅ API format is consistent")
        print()
        print("🎯 THE USER'S ISSUE IS FIXED!")
        print("   Users will now see their actual portfolio data")
        print("   instead of the default AMZN/NVDA/COM entries.")
        print()
        print("📝 What was fixed:")
        print("   - Improved ticker column detection for simple formats")
        print("   - Enhanced positional column detection")
        print("   - Better content analysis for amount vs price columns")
        print("   - Automatic date column detection")
        print("   - Removed fallback to default data on processing errors")
        
    else:
        print("❌ Some tests failed!")
        print("   The user may still experience issues.")
        if not test1:
            print("   - Default data issue not fully resolved")
        if not test2:
            print("   - API format inconsistency")

if __name__ == "__main__":
    main()
