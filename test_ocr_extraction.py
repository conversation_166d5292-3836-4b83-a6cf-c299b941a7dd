#!/usr/bin/env python3
"""
Test script for OCR extraction improvements
"""

import sys
import os
import requests
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService

def test_ocr_space_api():
    """Test the OCR.space API directly."""
    print("Testing OCR.space API...")
    
    # Create a simple test image with text (we'll use a placeholder)
    # In real usage, this would be actual image data
    test_text = "Testing OCR extraction"
    
    try:
        # Test the API endpoint
        url = "https://api.ocr.space/parse/image"
        
        # Create a simple test request
        data = {
            'apikey': 'helloworld',
            'language': 'eng',
            'isOverlayRequired': 'false',
            'detectOrientation': 'true',
            'scale': 'true',
            'OCREngine': '2',
            'url': 'https://via.placeholder.com/300x200/000000/FFFFFF?text=Portfolio+Test'
        }
        
        response = requests.post(url, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"OCR.space API Response: {result}")
            
            if result.get('ParsedResults'):
                text = result['ParsedResults'][0].get('ParsedText', '')
                print(f"Extracted text: '{text}'")
                return True
            else:
                print("No parsed results returned")
                return False
        else:
            print(f"HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"OCR.space API test failed: {e}")
        return False

def test_enhanced_extraction():
    """Test the enhanced extraction methods."""
    print("\nTesting enhanced extraction methods...")
    
    service = PortfolioImportService("test_api_key")
    
    # Test with sample portfolio text (simulating successful OCR)
    sample_text = """
    Mine beholdninger
    
    Alphabet Inc. NasdaqGS:GOOGL
    Antal: 13 stk
    GAK: 161,61 USD
    Markedsværdi: 2.462,85 USD
    Ureal.afkast: +9,9%
    
    Tesla Inc. NasdaqGS:TSLA
    Antal: 5 stk
    GAK: 250,00 USD
    Markedsværdi: 1.200,00 USD
    """
    
    print("Testing with sample Danish portfolio text...")
    result = service.extract_portfolio_from_text(sample_text)
    
    print(f"Extraction successful: {result.success}")
    print(f"Number of entries: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    for entry in result.portfolio_entries:
        print(f"  {entry.ticker}: {entry.shares} shares @ ${entry.buy_price}")
    
    return result.success

def test_error_handling():
    """Test error handling for failed OCR."""
    print("\nTesting error handling...")
    
    service = PortfolioImportService("test_api_key")
    
    # Test with OCR failure message
    failed_text = "OCR_EXTRACTION_FAILED: Unable to extract text from image."
    
    result = service.extract_portfolio_from_text(failed_text)
    
    print(f"Handled OCR failure correctly: {not result.success}")
    print(f"Error messages provided: {len(result.errors) > 0}")
    
    if result.errors:
        print("Error messages:")
        for error in result.errors:
            print(f"  - {error}")
    
    return not result.success

def test_multilingual_detection():
    """Test multilingual detection capabilities."""
    print("\nTesting multilingual detection...")
    
    service = PortfolioImportService("test_api_key")
    
    test_cases = [
        ("Danish", "Mine beholdninger aktier værdi pris antal DKK"),
        ("German", "Meine Aktien Wert Preis Anzahl EUR"),
        ("French", "Mes actions valeur prix quantité EUR"),
        ("English", "My portfolio stocks value price shares USD")
    ]
    
    for language, text in test_cases:
        detected = service.ai_extractor._detect_language(text)
        currency = service.ai_extractor._detect_primary_currency(text)
        print(f"{language} text -> Language: {detected}, Currency: {currency}")
    
    return True

if __name__ == "__main__":
    print("OCR Extraction Test Suite")
    print("=" * 50)
    
    # Test OCR.space API
    ocr_test = test_ocr_space_api()
    print(f"OCR.space API test: {'PASS' if ocr_test else 'FAIL'}")
    
    # Test enhanced extraction
    extraction_test = test_enhanced_extraction()
    print(f"Enhanced extraction test: {'PASS' if extraction_test else 'FAIL'}")
    
    # Test error handling
    error_test = test_error_handling()
    print(f"Error handling test: {'PASS' if error_test else 'FAIL'}")
    
    # Test multilingual detection
    multilingual_test = test_multilingual_detection()
    print(f"Multilingual detection test: {'PASS' if multilingual_test else 'FAIL'}")
    
    print("\n" + "=" * 50)
    
    overall_success = all([extraction_test, error_test, multilingual_test])
    print(f"Overall test result: {'PASS' if overall_success else 'FAIL'}")
    
    if not overall_success:
        print("\nSome tests failed. The OCR extraction may need further improvements.")
    else:
        print("\nAll core tests passed! The enhanced OCR system should work better.")
