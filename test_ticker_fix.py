#!/usr/bin/env python3
"""
Test the ticker extraction fix
"""

import os
import sys

# Set environment
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'

def test_ticker_extraction():
    """Test the ticker extraction fix with None values"""
    
    # Simulate the problematic Gemini response
    test_items = [
        {
            "ticker": None,
            "shares": None,
            "buy_price": None,
            "buy_price_currency": None,
            "current_price": 229.88,
            "current_price_currency": "USD",
            "current_value": 42987.50,
            "current_value_currency": "CZK",
            "amount_invested": None,
            "amount_invested_currency": None,
            "company_name": "Apple Inc",
            "purchase_date": None,
            "gain_loss_percent": 12.89,
            "currency_context": "Current value in Czech Koruna (CZK); current price in USD"
        },
        {
            "ticker": None,
            "shares": None,
            "buy_price": None,
            "buy_price_currency": None,
            "current_price": 127.91,
            "current_price_currency": "USD",
            "current_value": 51320.1,
            "current_value_currency": "CZK",
            "amount_invested": None,
            "amount_invested_currency": None,
            "company_name": "NVIDIA Corp",
            "purchase_date": None,
            "gain_loss_percent": 22.03,
            "currency_context": "Current value in Czech Koruna (CZK); current price in USD"
        }
    ]
    
    print("🧪 TESTING TICKER EXTRACTION FIX")
    print("=" * 50)
    
    for i, item in enumerate(test_items, 1):
        print(f"\n📋 Test {i}: {item.get('company_name', 'Unknown')}")
        
        # Test the fixed ticker extraction logic
        try:
            # Extract ticker - now directly available from improved prompt
            ticker_raw = item.get('ticker')
            ticker = (ticker_raw or '').strip().upper() if ticker_raw is not None else ''
            
            print(f"   Raw ticker: {ticker_raw}")
            print(f"   Processed ticker: '{ticker}'")
            
            # Enhanced ticker extraction - handle cases where Gemini returns company name instead of ticker
            if not ticker and item.get('company_name'):
                company_name = item.get('company_name', '').strip()
                if company_name:
                    print(f"   Company name: '{company_name}'")
                    
                    # Extract potential ticker from company name (e.g., "Apple Inc" -> "AAPL")
                    if 'apple' in company_name.lower():
                        ticker = 'AAPL'
                    elif 'nvidia' in company_name.lower():
                        ticker = 'NVDA'
                    elif 'microsoft' in company_name.lower():
                        ticker = 'MSFT'
                    elif 'google' in company_name.lower() or 'alphabet' in company_name.lower():
                        ticker = 'GOOGL'
                    elif 'tesla' in company_name.lower():
                        ticker = 'TSLA'
                    elif 'amazon' in company_name.lower():
                        ticker = 'AMZN'
                    else:
                        # Use first few letters as fallback
                        ticker = company_name.replace(' ', '').replace('.', '').replace(',', '')[:5].upper()
                    
                    print(f"   Fallback ticker: '{ticker}'")
            
            print(f"   ✅ Final ticker: '{ticker}'")
            
            if ticker:
                print(f"   ✅ SUCCESS: Extracted ticker '{ticker}' from company '{item.get('company_name', 'Unknown')}'")
            else:
                print(f"   ❌ FAILED: Could not extract ticker")
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎯 SUMMARY")
    print("=" * 20)
    print("✅ The 'NoneType' object has no attribute 'strip' error should be fixed")
    print("✅ Company names should be converted to ticker symbols")
    print("✅ Users should no longer see 'AI extraction failed, falling back to pattern matching'")
    print("✅ Users should no longer see 'No valid portfolio entries found'")

if __name__ == "__main__":
    test_ticker_extraction()
