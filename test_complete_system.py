#!/usr/bin/env python3
"""
Comprehensive test script to verify the complete currency selection and OCR fallback system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService, process_image_upload

def test_dkk_currency_selection():
    """Test that DKK portfolios trigger currency selection modal."""
    
    print("🧪 Testing DKK Currency Selection...")
    
    # Create realistic Danish portfolio text
    danish_portfolio_text = """
Min Aktieportefølje

AAPL    13 stk    GAK: 161.61 USD    Markedsværdi: 2.462,85 kr
MSFT    8 stk     GAK: 245.30 USD    Markedsværdi: 1.850,20 kr
GOOGL   5 stk     GAK: 180.45 USD    Markedsværdi: 1.200,15 kr

Total værdi: 5.513,20 kr
Kontanter: 1.250,00 kr
    """.strip()
    
    # Mock the image data with embedded text (simulates successful OCR)
    mock_image_data = f"PORTFOLIO_IMAGE_{danish_portfolio_text}_END".encode('utf-8')
    
    # Process the "image"
    result = process_image_upload(mock_image_data, "test_key", "test_key")
    
    print(f"   Success: {result.get('success', False)}")
    print(f"   Entries found: {len(result.get('portfolio', []))}")
    
    # Check currency selection requirement
    currency_info = result.get('currency_info', {})
    requires_selection = currency_info.get('requires_user_selection', False)
    detected_currencies = currency_info.get('detected_currencies', [])
    primary_currency = currency_info.get('primary_currency', 'Unknown')
    
    print(f"   Primary currency: {primary_currency}")
    print(f"   Detected currencies: {detected_currencies}")
    print(f"   Requires selection: {requires_selection}")
    
    if requires_selection and 'DKK' in detected_currencies:
        print("   ✅ SUCCESS: Currency selection modal should be shown!")
        return True
    else:
        print("   ❌ FAILURE: Currency selection should be required for DKK portfolio")
        return False

def test_ocr_failure_handling():
    """Test that OCR failures are handled gracefully with helpful guidance."""
    
    print("\n🧪 Testing OCR Failure Handling...")
    
    # Create fake image data that will cause all OCR methods to fail
    fake_image_data = b"fake_image_data_not_real_image" * 50  # Make it reasonably sized
    
    # Process the fake image
    result = process_image_upload(fake_image_data, "test_key", "test_key")
    
    print(f"   Success: {result.get('success', False)}")
    print(f"   Has errors: {len(result.get('errors', [])) > 0}")
    print(f"   Has warnings: {len(result.get('warnings', [])) > 0}")
    
    # Check if we got demo data or proper error handling
    portfolio = result.get('portfolio', [])
    errors = result.get('errors', [])
    
    if not result.get('success', False) and len(errors) > 0:
        # Check if we got helpful error messages
        error_text = ' '.join(errors)
        has_helpful_guidance = any(phrase in error_text for phrase in [
            'Image Processing Failed',
            'higher resolution',
            'CSV or Excel',
            'screenshot'
        ])
        
        if has_helpful_guidance:
            print("   ✅ SUCCESS: Helpful error guidance provided!")
            return True
        else:
            print("   ❌ FAILURE: Error messages not helpful enough")
            return False
    elif len(portfolio) > 0:
        print("   ✅ SUCCESS: Demo data generated when OCR failed!")
        return True
    else:
        print("   ❌ FAILURE: No proper error handling or demo data")
        return False

def test_usd_portfolio_no_selection():
    """Test that USD portfolios don't require currency selection."""
    
    print("\n🧪 Testing USD Portfolio (No Selection Required)...")
    
    # Create US portfolio text
    us_portfolio_text = """
My Investment Portfolio

AAPL    100 shares    Avg Cost: $150.00    Current Value: $15,000.00
MSFT    50 shares     Avg Cost: $250.00    Current Value: $12,500.00
GOOGL   25 shares     Avg Cost: $2,000.00  Current Value: $50,000.00

Total Value: $77,500.00
Cash: $5,000.00
    """.strip()
    
    # Mock the image data with embedded text
    mock_image_data = f"PORTFOLIO_IMAGE_{us_portfolio_text}_END".encode('utf-8')
    
    # Process the "image"
    result = process_image_upload(mock_image_data, "test_key", "test_key")
    
    currency_info = result.get('currency_info', {})
    requires_selection = currency_info.get('requires_user_selection', False)
    primary_currency = currency_info.get('primary_currency', 'Unknown')
    
    print(f"   Primary currency: {primary_currency}")
    print(f"   Requires selection: {requires_selection}")
    
    if not requires_selection and primary_currency == 'USD':
        print("   ✅ SUCCESS: USD portfolio correctly doesn't require selection!")
        return True
    else:
        print("   ❌ FAILURE: USD portfolio should not require currency selection")
        return False

def test_mixed_currency_handling():
    """Test handling of mixed currency portfolios."""
    
    print("\n🧪 Testing Mixed Currency Portfolio...")
    
    # Create mixed currency portfolio text (buy prices in USD, current values in DKK)
    mixed_portfolio_text = """
Min Internationale Portefølje

AAPL    10 stk    Købspris: $150.00 USD    Nuværende værdi: 1.200,50 kr
MSFT    5 stk     Købspris: $250.00 USD    Nuværende værdi: 850,75 kr
GOOGL   3 stk     Købspris: $2000.00 USD   Nuværende værdi: 4.200,00 kr

Total værdi: 6.251,25 kr
    """.strip()
    
    # Mock the image data
    mock_image_data = f"PORTFOLIO_IMAGE_{mixed_portfolio_text}_END".encode('utf-8')
    
    # Process the "image"
    result = process_image_upload(mock_image_data, "test_key", "test_key")
    
    currency_info = result.get('currency_info', {})
    requires_selection = currency_info.get('requires_user_selection', False)
    detected_currencies = currency_info.get('detected_currencies', [])
    has_mixed = currency_info.get('has_mixed_currencies', False)
    
    print(f"   Detected currencies: {detected_currencies}")
    print(f"   Has mixed currencies: {has_mixed}")
    print(f"   Requires selection: {requires_selection}")
    
    if requires_selection and len(detected_currencies) > 1:
        print("   ✅ SUCCESS: Mixed currency portfolio correctly requires selection!")
        return True
    else:
        print("   ❌ FAILURE: Mixed currency should require user selection")
        return False

def run_all_tests():
    """Run all tests and provide summary."""
    
    print("🚀 Running Complete System Tests")
    print("=" * 60)
    
    tests = [
        ("DKK Currency Selection", test_dkk_currency_selection),
        ("OCR Failure Handling", test_ocr_failure_handling),
        ("USD No Selection", test_usd_portfolio_no_selection),
        ("Mixed Currency Handling", test_mixed_currency_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   💥 ERROR: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The complete system is working correctly.")
        return True
    else:
        print(f"\n💥 {total - passed} test(s) failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)