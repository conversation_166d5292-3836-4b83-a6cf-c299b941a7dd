#!/usr/bin/env python3
"""
Debug the AI extraction error specifically
"""

from portfolio_import import AIPortfolioExtractor

def debug_ai_extraction():
    """Debug the AI extraction error"""
    print("🔍 Debugging AI Extraction Error")
    print("=" * 50)
    
    # The problematic text
    text = """
Alphabet Inc.
NasdaqGS:GOOGL
10
161
DKK 12,216.61

ASML Holding N.V.
NasdaqGS:ASML
2
668.5
DKK 9,279.65
"""
    
    print("Text:")
    print(repr(text))
    print()
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    try:
        print("Testing extract_portfolio_data_with_ai:")
        result = extractor.extract_portfolio_data_with_ai(text)
        print(f"Success: {result['success']}")
        print(f"Portfolio: {result['portfolio']}")
        print(f"Errors: {result.get('errors', [])}")
        print(f"Warnings: {result.get('warnings', [])}")
    except Exception as e:
        print(f"Error in extract_portfolio_data_with_ai: {e}")
        import traceback
        traceback.print_exc()
        
        # Try the individual methods
        print("\nTesting individual methods:")
        
        try:
            print("Testing _intelligent_extraction:")
            result = extractor._intelligent_extraction(text)
            print(f"Result: {result}")
        except Exception as e2:
            print(f"Error in _intelligent_extraction: {e2}")
            import traceback
            traceback.print_exc()
            
            # Try the table format parsing
            try:
                print("\nTesting _parse_table_format:")
                result = extractor._parse_table_format(text)
                print(f"Result: {result}")
            except Exception as e3:
                print(f"Error in _parse_table_format: {e3}")
                import traceback
                traceback.print_exc()

if __name__ == "__main__":
    debug_ai_extraction()
