#!/usr/bin/env python3
"""
Test script to verify the portfolio import fixes for currency confusion and calculation errors.

This script tests the enhanced Gemini AI prompt and processing logic to ensure:
1. Currency confusion (USD vs DKK) is resolved
2. Share calculation errors are fixed
3. Amount invested interpretation is correct
4. Currency label handling works properly
5. Gain/loss calculation is accurate
6. Purchase dates are handled correctly
7. Share field interpretation preserves precision
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to the path so we can import portfolio_import
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import AIPortfolioExtractor

def test_mixed_currency_scenario():
    """Test the exact scenario described in the user's issue."""
    
    # Sample text that represents the problematic scenario
    test_text = """
    Portfolio Holdings:
    
    GOOGL - Alphabet Inc
    Antal: 83.26 stk
    GAK: 161.61 USD
    Markedsværdi: 15,848 kr
    Afkast: 23%
    Dato: 23.07.2025
    
    ASML - ASML Holding
    Antal: 44.64 stk  
    GAK: 189.45 USD
    Markedsværdi: 2,462.85 USD
    Afkast: -5%
    Dato: 23.07.2025
    
    AAPL - Apple Inc
    Antal: 13.0 stk
    GAK: 161.61 USD
    Markedsværdi: 2,462.85 USD
    Afkast: 15%
    Dato: 23.07.2025
    """
    
    print("🧪 TESTING MIXED CURRENCY SCENARIO")
    print("=" * 50)
    print(f"Input text:\n{test_text}")
    print("=" * 50)
    
    # Initialize the extractor
    extractor = AIPortfolioExtractor(
        google_vision_api_key="test_key",
        eodhd_api_key="test_key"
    )
    
    # Test the extraction
    result = extractor.extract_portfolio_data_with_ai(test_text)
    
    print(f"🔍 EXTRACTION RESULTS:")
    print(f"Success: {result['success']}")
    print(f"Method: {result.get('extraction_method', 'unknown')}")
    print(f"Detected Language: {result.get('detected_language', 'unknown')}")
    print(f"Detected Currency: {result.get('detected_currency', 'unknown')}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Warnings: {result.get('warnings', [])}")
    
    print(f"\n📊 PORTFOLIO ENTRIES ({len(result.get('portfolio', []))} found):")
    
    for i, entry in enumerate(result.get('portfolio', []), 1):
        print(f"\n--- Entry {i}: {entry.get('ticker', 'Unknown')} ---")
        print(f"  Shares: {entry.get('shares', 'N/A')}")
        print(f"  Buy Price: {entry.get('buy_price', 'N/A')} {entry.get('buy_price_currency', entry.get('currency', 'N/A'))}")
        print(f"  Current Price: {entry.get('current_price', 'N/A')}")
        print(f"  Current Value: {entry.get('current_value', 'N/A')} {entry.get('current_value_currency', entry.get('currency', 'N/A'))}")
        print(f"  Amount Invested: {entry.get('amount_invested', 'N/A')} {entry.get('buy_price_currency', entry.get('currency', 'N/A'))}")
        print(f"  Purchase Date: {entry.get('purchase_date', 'N/A')}")
        print(f"  Gain/Loss %: {entry.get('gain_loss_percent', 'N/A')}")
        
        # Validation checks
        shares = entry.get('shares', 0)
        buy_price = entry.get('buy_price', 0)
        current_value = entry.get('current_value', 0)
        amount_invested = entry.get('amount_invested', 0)
        
        if shares > 0 and buy_price > 0:
            calculated_invested = shares * buy_price
            print(f"  ✓ Calculated Investment: {shares} × {buy_price} = {calculated_invested:.2f}")
            if amount_invested > 0:
                diff_pct = abs(calculated_invested - amount_invested) / max(calculated_invested, amount_invested) * 100
                print(f"  ✓ Investment Consistency: {diff_pct:.1f}% difference")
        
        if 'validation_warnings' in entry:
            print(f"  ⚠️  Validation Warnings: {entry['validation_warnings']}")
    
    return result

def test_number_parsing():
    """Test the enhanced number parsing for European formats."""
    
    print("\n\n🔢 TESTING NUMBER PARSING")
    print("=" * 50)
    
    extractor = AIPortfolioExtractor("test_key", "test_key")
    
    test_cases = [
        ("15,848", 15848.0, "European thousands with comma"),
        ("2,462.85", 2462.85, "US format with comma thousands"),
        ("2.462,85", 2462.85, "European format with period thousands"),
        ("83.26", 83.26, "Simple decimal"),
        ("161.61", 161.61, "Simple decimal"),
        ("15.848", 15848.0, "European thousands with period"),
        ("1,234.56", 1234.56, "US format"),
        ("1.234,56", 1234.56, "European format"),
    ]
    
    for test_input, expected, description in test_cases:
        result = extractor._parse_financial_value(test_input)
        status = "✅" if abs(result - expected) < 0.01 else "❌"
        print(f"{status} {description}: '{test_input}' → {result} (expected: {expected})")

def test_currency_detection():
    """Test the enhanced currency detection."""
    
    print("\n\n💱 TESTING CURRENCY DETECTION")
    print("=" * 50)
    
    extractor = AIPortfolioExtractor("test_key", "test_key")
    
    test_cases = [
        ("GAK 161.61 USD, Markedsværdi 15,848 kr", "Mixed USD/DKK"),
        ("Price: $123.45, Value: €456.78", "Mixed USD/EUR"),
        ("Pris: 123,45 kr", "Danish Kroner"),
        ("Value: $1,234.56", "US Dollars"),
        ("Værdi: 1.234,56 €", "Euros"),
    ]
    
    for test_text, description in test_cases:
        detected = extractor._detect_primary_currency(test_text)
        print(f"📝 {description}: '{test_text[:30]}...' → {detected}")

if __name__ == "__main__":
    print("🚀 PORTFOLIO IMPORT FIXES TEST SUITE")
    print("Testing enhanced currency handling and calculation accuracy")
    print("=" * 70)
    
    try:
        # Run the tests
        result = test_mixed_currency_scenario()
        test_number_parsing()
        test_currency_detection()
        
        print("\n\n✅ TEST SUITE COMPLETED")
        print("=" * 70)
        
        # Summary
        if result.get('success'):
            print("🎉 Portfolio extraction was successful!")
            portfolio_count = len(result.get('portfolio', []))
            print(f"📊 Extracted {portfolio_count} portfolio entries")
            
            if result.get('warnings'):
                print(f"⚠️  {len(result['warnings'])} warnings generated")
            
            if result.get('errors'):
                print(f"❌ {len(result['errors'])} errors encountered")
        else:
            print("❌ Portfolio extraction failed")
            print(f"Errors: {result.get('errors', [])}")
            
    except Exception as e:
        print(f"❌ TEST SUITE FAILED: {e}")
        import traceback
        traceback.print_exc()
