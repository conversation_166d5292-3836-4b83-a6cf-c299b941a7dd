<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Currency Selection Modal</title>
    <style>
        /* Basic modal styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .currency-modal {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px;
            border-top: 1px solid #e5e7eb;
            gap: 16px;
        }

        .currency-option {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }

        .currency-option:hover {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }

        .currency-option.selected {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.2);
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
        }

        .btn-secondary {
            background: transparent;
            color: #374151;
            border: 2px solid #e5e7eb;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
        }

        .ai-message {
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
            border: 2px solid rgba(147, 51, 234, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Currency Selection Modal Test</h1>
    <button onclick="showTestModal()">Show Currency Selection Modal</button>
    <button onclick="testWithDKK()">Test DKK Currency Selection</button>

    <!-- Currency Selection Modal -->
    <div id="currencySelectionModal" class="modal-overlay" style="display: none;">
        <div class="modal-content currency-modal">
            <div class="modal-header">
                <h3>🪙 Multiple Currencies Detected</h3>
                <button type="button" class="modal-close" onclick="closeCurrencyModal()">
                    ✕
                </button>
            </div>

            <div class="modal-body">
                <div class="currency-detection-info">
                    <div class="gemini-question" id="geminiQuestion" style="display: none;">
                        <div class="ai-message">
                            <div class="ai-content">
                                <h4>🤖 AI Currency Detection</h4>
                                <p id="geminiQuestionText"></p>
                            </div>
                        </div>
                    </div>

                    <p class="detection-message" id="defaultDetectionMessage">
                        ℹ️ We detected multiple currencies in your portfolio. Please select your preferred display currency:
                    </p>

                    <div class="currency-analysis" id="currencyAnalysis">
                        <!-- Currency analysis will be populated here -->
                    </div>
                </div>

                <div class="currency-options" id="currencyOptions">
                    <!-- Currency options will be populated here -->
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeCurrencyModal()">
                    ← Go Back
                </button>
                <button type="button" class="btn-primary" id="confirmCurrencyButton" onclick="confirmCurrencySelection()">
                    ✓ Continue with Selected Currency
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedCurrency = 'USD';

        function showTestModal() {
            console.log('Showing test modal...');
            const modal = document.getElementById('currencySelectionModal');
            
            // Test with mixed currencies
            const testResult = {
                currency_info: {
                    detected_currencies: ['USD', 'EUR', 'DKK'],
                    primary_currency: 'EUR',
                    requires_user_selection: true,
                    gemini_question: 'I found multiple currencies in your portfolio (USD, EUR, DKK). Which one should I use as the primary display currency?',
                    currency_analysis: {
                        'USD': { total_usage: 5, percentage: 33 },
                        'EUR': { total_usage: 7, percentage: 47 },
                        'DKK': { total_usage: 3, percentage: 20 }
                    }
                }
            };

            showCurrencySelectionModal(testResult);
        }

        function testWithDKK() {
            console.log('Testing DKK currency selection...');
            
            // Test with DKK currency (should trigger selection)
            const dkkResult = {
                currency_info: {
                    detected_currencies: ['DKK'],
                    primary_currency: 'DKK',
                    requires_user_selection: true,
                    gemini_question: 'I detected DKK currency in your portfolio. Is this correct, or would you prefer to display amounts in a different currency?',
                    currency_analysis: {
                        'DKK': { total_usage: 10, percentage: 100 }
                    }
                }
            };

            showCurrencySelectionModal(dkkResult);
        }

        function showCurrencySelectionModal(result) {
            console.log('showCurrencySelectionModal called with:', result);
            
            const modal = document.getElementById('currencySelectionModal');
            const currencyAnalysis = document.getElementById('currencyAnalysis');
            const currencyOptions = document.getElementById('currencyOptions');
            const geminiQuestion = document.getElementById('geminiQuestion');
            const geminiQuestionText = document.getElementById('geminiQuestionText');
            const defaultDetectionMessage = document.getElementById('defaultDetectionMessage');
            
            console.log('Modal elements found:', {
                modal: !!modal,
                currencyAnalysis: !!currencyAnalysis,
                currencyOptions: !!currencyOptions,
                geminiQuestion: !!geminiQuestion,
                geminiQuestionText: !!geminiQuestionText,
                defaultDetectionMessage: !!defaultDetectionMessage
            });

            // Handle Gemini AI question if present
            if (result.currency_info && result.currency_info.gemini_question) {
                geminiQuestionText.textContent = result.currency_info.gemini_question;
                geminiQuestion.style.display = 'block';
                defaultDetectionMessage.style.display = 'none';
                console.log('Showing Gemini question:', result.currency_info.gemini_question);
            } else {
                geminiQuestion.style.display = 'none';
                defaultDetectionMessage.style.display = 'block';
                console.log('Showing default detection message');
            }

            // Populate currency analysis
            if (result.currency_info && result.currency_info.currency_analysis) {
                let analysisHTML = '<h4>📊 Currency Usage Analysis:</h4>';

                Object.entries(result.currency_info.currency_analysis).forEach(([currency, stats]) => {
                    const percentage = Math.round(stats.percentage);
                    analysisHTML += `
                        <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
                            <span><strong>${currency}</strong></span>
                            <span>${stats.total_usage} fields (${percentage}%)</span>
                        </div>
                    `;
                });

                currencyAnalysis.innerHTML = analysisHTML;
                console.log('Currency analysis populated');
            }

            // Populate currency options
            const currencies = result.currency_info.detected_currencies || [];
            const primaryCurrency = result.currency_info.primary_currency || currencies[0];

            let optionsHTML = '';
            currencies.forEach((currency, index) => {
                const isSelected = currency === primaryCurrency;
                const stats = result.currency_info.currency_analysis[currency] || {};
                const displayName = getCurrencyDisplayName(currency);

                optionsHTML += `
                    <div class="currency-option ${isSelected ? 'selected' : ''}" onclick="selectCurrency('${currency}')">
                        <input type="radio" name="selectedCurrency" value="${currency}" ${isSelected ? 'checked' : ''}>
                        <div style="margin-left: 12px;">
                            <div style="font-weight: 600; margin-bottom: 4px;">${displayName}</div>
                            <div style="color: #6b7280; font-size: 0.9rem;">
                                Used in ${stats.total_usage || 0} fields
                                ${isSelected ? ' • Recommended based on usage' : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            currencyOptions.innerHTML = optionsHTML;
            console.log('Currency options populated');

            // Show the modal
            console.log('Showing currency selection modal...');
            modal.style.display = 'flex';
            console.log('Modal display set to flex');

            // Set the selected currency
            selectedCurrency = primaryCurrency;
            console.log('Selected currency set to:', primaryCurrency);
        }

        function getCurrencyDisplayName(currency) {
            const currencyNames = {
                'USD': '🇺🇸 US Dollar ($)',
                'EUR': '🇪🇺 Euro (€)',
                'GBP': '🇬🇧 British Pound (£)',
                'DKK': '🇩🇰 Danish Krone (kr)',
                'SEK': '🇸🇪 Swedish Krona (kr)',
                'NOK': '🇳🇴 Norwegian Krone (kr)',
                'JPY': '🇯🇵 Japanese Yen (¥)',
                'CHF': '🇨🇭 Swiss Franc (CHF)',
                'CAD': '🇨🇦 Canadian Dollar (C$)',
                'AUD': '🇦🇺 Australian Dollar (A$)'
            };

            return currencyNames[currency] || `${currency}`;
        }

        function selectCurrency(currency) {
            console.log('Currency selected:', currency);
            
            // Update radio button
            const radio = document.querySelector(`input[name="selectedCurrency"][value="${currency}"]`);
            if (radio) {
                radio.checked = true;
            }

            // Update visual selection
            document.querySelectorAll('.currency-option').forEach(option => {
                option.classList.remove('selected');
            });

            const selectedOption = document.querySelector(`input[name="selectedCurrency"][value="${currency}"]`).closest('.currency-option');
            if (selectedOption) {
                selectedOption.classList.add('selected');
            }

            selectedCurrency = currency;
        }

        function confirmCurrencySelection() {
            console.log('Currency selection confirmed:', selectedCurrency);
            alert(`Currency selected: ${selectedCurrency}`);
            closeCurrencyModal();
        }

        function closeCurrencyModal() {
            console.log('Closing currency modal');
            const modal = document.getElementById('currencySelectionModal');
            modal.style.display = 'none';
        }
    </script>
</body>
</html>