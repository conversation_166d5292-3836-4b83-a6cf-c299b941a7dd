#!/usr/bin/env python3
"""
Quick test for the fixed portfolio import system
"""

def test_fixes():
    """Test the fixes for image and spreadsheet processing"""
    print("Testing Portfolio Import Fixes")
    print("=" * 40)
    
    try:
        from portfolio_import import process_image_upload, process_spreadsheet_upload
        
        # Test 1: Image processing with mock OCR
        print("\n1. Testing image processing...")
        dummy_image = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
        result = process_image_upload(dummy_image, "test_key")
        print(f"   Image processing: {'✅ PASS' if result['success'] else '❌ FAIL'}")
        if result['success']:
            print(f"   Found {len(result['portfolio'])} entries")
        
        # Test 2: Spreadsheet with simple column names (A, B, C, D)
        print("\n2. Testing spreadsheet with simple column names (A, B, C, D)...")

        with open('test_simple_columns.csv', 'rb') as f:
            csv_data = f.read()

        result = process_spreadsheet_upload(csv_data, 'test_simple_columns.csv', "test_key")
        print(f"   Simple columns processing: {'✅ PASS' if result['success'] else '❌ FAIL'}")
        if result['success']:
            print(f"   Found {len(result['portfolio'])} entries")
            for entry in result['portfolio'][:2]:  # Show first 2 entries
                print(f"     {entry['ticker']}: ${entry['amount_invested']} @ ${entry['buy_price']}")
        else:
            print(f"   Errors: {result.get('errors', [])}")
            print(f"   Warnings: {result.get('warnings', [])}")

        # Test 3: Spreadsheet with proper column names
        print("\n3. Testing spreadsheet with proper column names...")
        import pandas as pd

        test_data = {
            'Stock Symbol': ['AAPL', 'MSFT'],
            'Avg Cost Basis': [150.50, 250.75],
            'Total Investment': [1500.00, 2000.00],
            'Purchase Date': ['2023-04-15', '2023-03-20']
        }

        df = pd.DataFrame(test_data)
        csv_data = df.to_csv(index=False).encode('utf-8')

        result = process_spreadsheet_upload(csv_data, 'test.csv', "test_key")
        print(f"   Named columns processing: {'✅ PASS' if result['success'] else '❌ FAIL'}")
        if result['success']:
            print(f"   Found {len(result['portfolio'])} entries")
        else:
            print(f"   Errors: {result.get('errors', [])}")
            print(f"   Warnings: {result.get('warnings', [])}")
        
        print("\n" + "=" * 40)
        print("✅ All fixes working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    test_fixes()
