#!/usr/bin/env python3
"""
Test script to verify the enhanced currency detection and selection UI works correctly.

This script tests the new currency detection features and API response format.
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to the path so we can import portfolio_import
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService

def test_currency_detection_and_api_response():
    """Test the enhanced currency detection and API response format."""
    
    print("🧪 TESTING ENHANCED CURRENCY DETECTION & API RESPONSE")
    print("=" * 70)
    
    # Sample OCR text with mixed currencies (the real scenario from user logs)
    mixed_currency_text = """Alphabet A\t\r\nVæerdi\tAfkast\tI dag\tSeneste\t\r\n15.848 kr\t+10,77%\t+0,38%\t192,06 USD\t\r\na\tAmazon.com\t\r\nVæerdi\tAfkast\t1 dag\tSeneste\t\r\n18.858 kr\t+17,21%\t+0,36%\t228,29 USD\t\r\nASML\tASML Holding\t\r\nVardi\tAfkast\tI dag\tSeneste\t\r\n31.962 kr\t-4,27%\t+1,78%\t718,07 USD\t\r\nUber\tUber Technologies\t\r\nVæerdi\tAfkast\tI dag\tSeneste\t\r\n5.886 kr\t+11,14%\t+0,56%\t92,30 USD\t\r\n"""
    
    print(f"📝 Testing with mixed currency OCR text (DKK current values, USD prices)")
    
    try:
        # Initialize the service
        google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
        eodhd_api_key = "test_key"
        
        service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
        
        # Extract portfolio data
        print(f"\n🔧 Testing PortfolioImportService.extract_portfolio_from_text()...")
        result = service.extract_portfolio_from_text(mixed_currency_text)
        
        # Format for API (this includes the new currency information)
        print(f"\n🔧 Testing enhanced format_for_api()...")
        api_response = service.format_for_api(result)
        
        print(f"\n✅ API Response Generated!")
        print(f"   Success: {api_response.get('success', False)}")
        print(f"   Portfolio entries: {len(api_response.get('portfolio', []))}")
        
        # Test the new currency information
        currency_info = api_response.get('currency_info', {})
        print(f"\n💱 CURRENCY INFORMATION:")
        print(f"   Detected currencies: {currency_info.get('detected_currencies', [])}")
        print(f"   Primary currency: {currency_info.get('primary_currency', 'N/A')}")
        print(f"   Has mixed currencies: {currency_info.get('has_mixed_currencies', False)}")
        print(f"   Requires user selection: {currency_info.get('requires_user_selection', False)}")
        
        # Show currency analysis
        currency_analysis = currency_info.get('currency_analysis', {})
        if currency_analysis:
            print(f"\n📊 CURRENCY USAGE ANALYSIS:")
            for currency, stats in currency_analysis.items():
                print(f"   {currency}:")
                print(f"     - Buy price usage: {stats.get('buy_price_usage', 0)} entries")
                print(f"     - Current value usage: {stats.get('current_value_usage', 0)} entries")
                print(f"     - Total usage: {stats.get('total_usage', 0)} fields")
                print(f"     - Percentage: {stats.get('percentage', 0):.1f}%")
        
        # Show portfolio entries with currency details
        print(f"\n📊 PORTFOLIO ENTRIES WITH CURRENCY DETAILS:")
        for i, entry in enumerate(api_response.get('portfolio', []), 1):
            print(f"   --- Entry {i}: {entry.get('ticker', 'Unknown')} ---")
            print(f"     Company: {entry.get('company_name', 'N/A')}")
            print(f"     Shares: {entry.get('shares', 'N/A')}")
            print(f"     Buy Price: {entry.get('buy_price', 'N/A')} {entry.get('buy_price_currency', 'N/A')}")
            print(f"     Current Price: {entry.get('current_price', 'N/A')} USD")
            print(f"     Current Value: {entry.get('current_value', 'N/A')} {entry.get('current_value_currency', 'N/A')}")
            print(f"     Gain/Loss: {entry.get('gain_loss_percent', 'N/A')}%")
        
        return api_response
        
    except Exception as e:
        print(f"❌ Currency detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_single_currency_scenario():
    """Test with a single currency to ensure it doesn't trigger the selection modal."""
    
    print(f"\n🧪 TESTING SINGLE CURRENCY SCENARIO")
    print("=" * 70)
    
    # Sample text with only USD
    single_currency_text = """
    Portfolio Holdings:
    
    AAPL - Apple Inc
    Shares: 100
    Buy Price: $150.00 USD
    Current Value: $17,500.00 USD
    Current Price: $175.00 USD
    
    MSFT - Microsoft Corp
    Shares: 50
    Buy Price: $200.00 USD
    Current Value: $12,500.00 USD
    Current Price: $250.00 USD
    """
    
    try:
        service = PortfolioImportService("test_key", "test_key")
        result = service.extract_portfolio_from_text(single_currency_text)
        api_response = service.format_for_api(result)
        
        currency_info = api_response.get('currency_info', {})
        print(f"💱 Single Currency Test Results:")
        print(f"   Detected currencies: {currency_info.get('detected_currencies', [])}")
        print(f"   Has mixed currencies: {currency_info.get('has_mixed_currencies', False)}")
        print(f"   Requires user selection: {currency_info.get('requires_user_selection', False)}")
        
        if not currency_info.get('requires_user_selection', True):
            print(f"✅ Single currency scenario correctly bypasses selection modal")
        else:
            print(f"❌ Single currency scenario incorrectly triggers selection modal")
            
        return api_response
        
    except Exception as e:
        print(f"❌ Single currency test failed: {e}")
        return None

def test_ui_integration_data():
    """Test that the API response provides all data needed for the UI."""
    
    print(f"\n🧪 TESTING UI INTEGRATION DATA")
    print("=" * 70)
    
    # Use the mixed currency scenario
    mixed_currency_text = """GOOGL: 83.26 stk, GAK: 161.61 USD, Markedsværdi: 15,848 kr
ASML: 44.64 stk, GAK: 189.45 USD, Markedsværdi: 31,962 kr"""
    
    try:
        service = PortfolioImportService("test_key", "test_key")
        result = service.extract_portfolio_from_text(mixed_currency_text)
        api_response = service.format_for_api(result)
        
        print(f"🔧 Checking UI integration requirements...")
        
        # Check required fields for currency selection UI
        required_fields = [
            'currency_info',
            'currency_info.detected_currencies',
            'currency_info.primary_currency',
            'currency_info.has_mixed_currencies',
            'currency_info.requires_user_selection',
            'currency_info.currency_analysis'
        ]
        
        all_present = True
        for field in required_fields:
            keys = field.split('.')
            current = api_response
            
            try:
                for key in keys:
                    current = current[key]
                print(f"   ✅ {field}: Present")
            except (KeyError, TypeError):
                print(f"   ❌ {field}: Missing")
                all_present = False
        
        if all_present:
            print(f"\n✅ All required fields for UI integration are present!")
            print(f"✅ The currency selection modal should work correctly!")
        else:
            print(f"\n❌ Some required fields are missing for UI integration")
            
        return api_response
        
    except Exception as e:
        print(f"❌ UI integration test failed: {e}")
        return None

if __name__ == "__main__":
    print("🚀 CURRENCY SELECTION UI TEST SUITE")
    print("Testing enhanced currency detection and selection UI integration")
    print("=" * 80)
    
    try:
        # Test 1: Mixed currency detection
        mixed_result = test_currency_detection_and_api_response()
        
        # Test 2: Single currency scenario
        single_result = test_single_currency_scenario()
        
        # Test 3: UI integration data
        ui_result = test_ui_integration_data()
        
        print("\n\n✅ CURRENCY SELECTION UI TEST COMPLETED")
        print("=" * 80)
        
        # Summary
        if mixed_result and mixed_result.get('currency_info', {}).get('requires_user_selection'):
            print("🎉 Mixed currency detection: SUCCESS!")
            print("✅ Currency selection modal will be triggered for mixed currencies")
        else:
            print("❌ Mixed currency detection: FAILED")
            
        if single_result and not single_result.get('currency_info', {}).get('requires_user_selection', True):
            print("✅ Single currency handling: SUCCESS!")
            print("✅ No modal will be shown for single currency portfolios")
        else:
            print("❌ Single currency handling: FAILED")
            
        if ui_result:
            print("✅ UI integration data: SUCCESS!")
            print("✅ All required data is provided for the currency selection UI")
        else:
            print("❌ UI integration data: FAILED")
            
        print(f"\n🎯 OVERALL RESULT:")
        if mixed_result and single_result and ui_result:
            print("🎉 COMPLETE SUCCESS!")
            print("✅ Users will see a beautiful currency selection modal for mixed currencies")
            print("✅ The AI correctly reads currencies from images")
            print("✅ Single currency portfolios work seamlessly without interruption")
            print("✅ The UI has all the data it needs for a great user experience")
        else:
            print("⚠️  PARTIAL SUCCESS - Some features may need refinement")
            
    except Exception as e:
        print(f"❌ TEST SUITE FAILED: {e}")
        import traceback
        traceback.print_exc()
