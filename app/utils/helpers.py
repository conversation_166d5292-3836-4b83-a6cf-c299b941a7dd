# -*- coding: utf-8 -*-
"""
Helper Utilities

This module contains helper functions and constants used throughout the application.
"""

import logging

logger = logging.getLogger(__name__)

# Industry Benchmark Data for Peer Comparison
INDUSTRY_BENCHMARKS = {
    'Technology': {
        'operating_margin': 0.25,
        'return_on_equity': 0.18,
        'return_on_assets': 0.12,
        'debt_to_equity': 0.15,
        'current_ratio': 2.5,
        'revenue_growth_5yr': 0.15,
        'roic': 0.20,
        'fcf_yield': 0.04,
        'p_fcf_ratio': 25.0,
        'p_e_ratio': 22.0,
        'gross_margin': 0.65,
        'net_margin': 0.20
    },
    'Healthcare': {
        'operating_margin': 0.20,
        'return_on_equity': 0.15,
        'return_on_assets': 0.08,
        'debt_to_equity': 0.25,
        'current_ratio': 2.0,
        'revenue_growth_5yr': 0.08,
        'roic': 0.15,
        'fcf_yield': 0.05,
        'p_fcf_ratio': 20.0,
        'p_e_ratio': 18.0,
        'gross_margin': 0.75,
        'net_margin': 0.15
    },
    'Financial Services': {
        'operating_margin': 0.30,
        'return_on_equity': 0.12,
        'return_on_assets': 0.01,
        'debt_to_equity': 2.5,
        'current_ratio': 1.1,
        'revenue_growth_5yr': 0.05,
        'roic': 0.08,
        'fcf_yield': 0.06,
        'p_fcf_ratio': 16.0,
        'p_e_ratio': 12.0,
        'gross_margin': 0.85,
        'net_margin': 0.25
    },
    'Consumer Discretionary': {
        'operating_margin': 0.08,
        'return_on_equity': 0.15,
        'return_on_assets': 0.06,
        'debt_to_equity': 0.40,
        'current_ratio': 1.5,
        'revenue_growth_5yr': 0.06,
        'roic': 0.12,
        'fcf_yield': 0.04,
        'p_fcf_ratio': 25.0,
        'p_e_ratio': 20.0,
        'gross_margin': 0.35,
        'net_margin': 0.05
    },
    'Industrials': {
        'operating_margin': 0.10,
        'return_on_equity': 0.14,
        'return_on_assets': 0.06,
        'debt_to_equity': 0.35,
        'current_ratio': 1.8,
        'revenue_growth_5yr': 0.04,
        'roic': 0.10,
        'fcf_yield': 0.05,
        'p_fcf_ratio': 20.0,
        'p_e_ratio': 16.0,
        'gross_margin': 0.30,
        'net_margin': 0.08
    },
    'Energy': {
        'operating_margin': 0.12,
        'return_on_equity': 0.08,
        'return_on_assets': 0.04,
        'debt_to_equity': 0.45,
        'current_ratio': 1.3,
        'revenue_growth_5yr': -0.02,
        'roic': 0.06,
        'fcf_yield': 0.08,
        'p_fcf_ratio': 12.0,
        'p_e_ratio': 14.0,
        'gross_margin': 0.25,
        'net_margin': 0.10
    }
}

# Metric Key Sets
MONETARY_METRIC_KEYS = {
    # Highlights
    "Highlights.MarketCapitalization", "Highlights.EBITDA", "Highlights.WallStreetTargetPrice",
    "Highlights.BookValue", "Highlights.DividendShare", "Highlights.EarningsShare",
    "Highlights.EPSEstimateCurrentYear", "Highlights.EPSEstimateNextYear", "Highlights.EPSEstimateNextQuarter",
    "Highlights.RevenueTTM", "Highlights.RevenuePerShareTTM", "Highlights.GrossProfitTTM",
    "Highlights.DilutedEpsTTM",
    # Valuation
    "Valuation.EnterpriseValue",
    # Technicals
    "Technicals.52WeekHigh", "Technicals.52WeekLow", "Technicals.50DayMA", "Technicals.200DayMA",
    # Splits Dividends
    "SplitsDividends.ForwardAnnualDividendRate",
    # Analyst Ratings
    "AnalystRatings.TargetPrice",
    # Holders
    "Holders.InstitutionsTotalValue", "Holders.FundsTotalValue",
    # Balance Sheet (Monetary Values)
    "Financials.Balance_Sheet.quarterly.latest.totalAssets", "Financials.Balance_Sheet.quarterly.latest.intangibleAssets",
    "Financials.Balance_Sheet.quarterly.latest.totalLiab", "Financials.Balance_Sheet.quarterly.latest.totalStockholderEquity",
    "Financials.Balance_Sheet.quarterly.latest.netTangibleAssets", "Financials.Balance_Sheet.quarterly.latest.totalCurrentAssets",
    "Financials.Balance_Sheet.quarterly.latest.totalCurrentLiabilities", "Financials.Balance_Sheet.quarterly.latest.commonStock",
    "Financials.Balance_Sheet.quarterly.latest.retainedEarnings",
    "Financials.Balance_Sheet.yearly.latest.totalAssets", "Financials.Balance_Sheet.yearly.latest.totalLiab",
    "Financials.Balance_Sheet.yearly.latest.totalStockholderEquity",
    # Income Statement (Monetary Values)
    "Financials.Income_Statement.quarterly.latest.totalRevenue", "Financials.Income_Statement.quarterly.latest.costOfRevenue",
    "Financials.Income_Statement.quarterly.latest.grossProfit", "Financials.Income_Statement.quarterly.latest.researchDevelopment",
    "Financials.Income_Statement.quarterly.latest.operatingIncome", "Financials.Income_Statement.quarterly.latest.netIncome",
    "Financials.Income_Statement.yearly.latest.totalRevenue", "Financials.Income_Statement.yearly.latest.grossProfit",
    "Financials.Income_Statement.yearly.latest.operatingIncome", "Financials.Income_Statement.yearly.latest.netIncome",
    # Cash Flow (Monetary Values)
    "Financials.Cash_Flow.quarterly.latest.totalCashFromOperatingActivities", "Financials.Cash_Flow.quarterly.latest.capitalExpenditures",
    "Financials.Cash_Flow.quarterly.latest.freeCashFlow",
    "Financials.Cash_Flow.yearly.latest.totalCashFromOperatingActivities", "Financials.Cash_Flow.yearly.latest.capitalExpenditures",
    "Financials.Cash_Flow.yearly.latest.freeCashFlow",
}

LARGE_NUMBER_METRIC_KEYS = {
    "Highlights.MarketCapitalization", "Highlights.EBITDA", "Highlights.RevenueTTM", "Highlights.GrossProfitTTM",
    "Valuation.EnterpriseValue",
    "Financials.Balance_Sheet.quarterly.latest.totalAssets", "Financials.Balance_Sheet.quarterly.latest.totalLiab",
    "Financials.Balance_Sheet.quarterly.latest.totalStockholderEquity", "Financials.Balance_Sheet.quarterly.latest.netTangibleAssets",
    "Financials.Balance_Sheet.quarterly.latest.totalCurrentAssets", "Financials.Balance_Sheet.quarterly.latest.totalCurrentLiabilities",
    "Financials.Balance_Sheet.yearly.latest.totalAssets", "Financials.Balance_Sheet.yearly.latest.totalLiab",
    "Financials.Balance_Sheet.yearly.latest.totalStockholderEquity",
    "Financials.Income_Statement.quarterly.latest.totalRevenue", "Financials.Income_Statement.quarterly.latest.costOfRevenue",
    "Financials.Income_Statement.quarterly.latest.grossProfit", "Financials.Income_Statement.quarterly.latest.researchDevelopment",
    "Financials.Income_Statement.quarterly.latest.operatingIncome", "Financials.Income_Statement.quarterly.latest.netIncome",
    "Financials.Income_Statement.yearly.latest.totalRevenue", "Financials.Income_Statement.yearly.latest.grossProfit",
    "Financials.Income_Statement.yearly.latest.operatingIncome", "Financials.Income_Statement.yearly.latest.netIncome",
    "Financials.Cash_Flow.quarterly.latest.totalCashFromOperatingActivities", "Financials.Cash_Flow.quarterly.latest.capitalExpenditures",
    "Financials.Cash_Flow.quarterly.latest.freeCashFlow",
    "Financials.Cash_Flow.yearly.latest.totalCashFromOperatingActivities", "Financials.Cash_Flow.yearly.latest.capitalExpenditures",
    "Financials.Cash_Flow.yearly.latest.freeCashFlow",
    "Holders.InstitutionsTotalValue", "Holders.FundsTotalValue"
}


def get_industry_benchmark(sector, metric_name):
    """
    Get industry benchmark for a specific metric.

    Args:
        sector (str): Company sector (e.g., 'Technology', 'Healthcare')
        metric_name (str): Metric name (e.g., 'roic', 'debt_to_equity')

    Returns:
        float or None: Benchmark value or None if not found
    """
    if not sector or not metric_name:
        return None

    # Normalize sector name
    sector_normalized = sector.strip()

    # Try exact match first
    if sector_normalized in INDUSTRY_BENCHMARKS:
        return INDUSTRY_BENCHMARKS[sector_normalized].get(metric_name)

    # Try partial matches for common variations
    sector_lower = sector_normalized.lower()
    for benchmark_sector in INDUSTRY_BENCHMARKS:
        if benchmark_sector.lower() in sector_lower or sector_lower in benchmark_sector.lower():
            return INDUSTRY_BENCHMARKS[benchmark_sector].get(metric_name)

    # Default to Technology sector if no match found
    return INDUSTRY_BENCHMARKS.get('Technology', {}).get(metric_name)


def get_metric_percentile_ranking(value, benchmark_value):
    """
    Calculate percentile ranking compared to industry benchmark.

    Args:
        value (float): Company's metric value
        benchmark_value (float): Industry benchmark value

    Returns:
        str: Percentile ranking description
    """
    if value is None or benchmark_value is None:
        return "N/A"

    ratio = value / benchmark_value

    if ratio >= 1.5:
        return "Top Quartile (Excellent)"
    elif ratio >= 1.2:
        return "Above Average (Good)"
    elif ratio >= 0.8:
        return "Average (Median)"
    elif ratio >= 0.6:
        return "Below Average (Weak)"
    else:
        return "Bottom Quartile (Poor)"


def get_metric_value_from_path(data_dict, path_str, default=None):
    """
    Retrieves a nested value from a dictionary using a dot-separated path string.
    Handles potential errors like missing keys or non-dict/list objects gracefully.
    Includes 'latest' keyword support for lists (assumes first element is latest)
    AND for dictionaries keyed by 'YYYY-MM-DD' dates (finds max date key).
    """
    if not isinstance(data_dict, (dict, list)) or not path_str:
        return default

    keys = path_str.split('.')
    current_level = data_dict

    try:
        for i, key in enumerate(keys):
            path_so_far = '.'.join(keys[:i+1])

            if key == 'latest':
                # Handle the 'latest' keyword
                if isinstance(current_level, list):
                    if current_level:  # Ensure list is not empty
                        current_level = current_level[0]  # Assume first element is latest
                    else:
                        logger.debug(f"Empty list encountered at path '{path_so_far}'")
                        return default
                elif isinstance(current_level, dict):
                    # Find the latest date key (max key that looks like a date)
                    date_keys = [k for k in current_level.keys() if isinstance(k, str) and len(k) >= 4]
                    if date_keys:
                        latest_key = max(date_keys)
                        current_level = current_level[latest_key]
                    else:
                        logger.debug(f"No date-like keys found in dict at path '{path_so_far}'")
                        return default
                else:
                    logger.debug(f"'latest' keyword used on non-list/non-dict at path '{path_so_far}'")
                    return default
            else:
                # Regular key access
                if isinstance(current_level, dict):
                    if key in current_level:
                        current_level = current_level[key]
                    else:
                        logger.debug(f"Key '{key}' not found in dict at path '{path_so_far}'")
                        return default
                elif isinstance(current_level, list):
                    try:
                        index = int(key)
                        if 0 <= index < len(current_level):
                            current_level = current_level[index]
                        else:
                            logger.debug(f"Index {index} out of range for list at path '{path_so_far}'")
                            return default
                    except ValueError:
                        logger.debug(f"Invalid list index '{key}' at path '{path_so_far}'")
                        return default
                else:
                    logger.debug(f"Cannot access key '{key}' on {type(current_level)} at path '{path_so_far}'")
                    return default

        return current_level

    except Exception as e:
        logger.warning(f"Error accessing path '{path_str}': {e}")
        return default
