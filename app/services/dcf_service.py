# -*- coding: utf-8 -*-
"""
DCF Service

This module contains DCF analysis and valuation calculation functions.
"""

from flask import current_app, session
from ..utils.formatters import safe_float
from ..utils.helpers import get_metric_value_from_path


def calculate_dcf(ticker, assumptions):
    """
    Main DCF calculation function.
    
    Args:
        ticker (str): Stock ticker symbol
        assumptions (dict): DCF assumptions including growth rate, discount rate, etc.
        
    Returns:
        dict: DCF calculation results
    """
    try:
        # Extract assumptions
        growth_rate = safe_float(assumptions.get('growth_rate'), 0.05)
        discount_rate = safe_float(assumptions.get('discount_rate'), 0.10)
        terminal_growth = safe_float(assumptions.get('terminal_growth'), 0.025)
        years = int(assumptions.get('years', 10))
        current_fcf = safe_float(assumptions.get('current_fcf'))
        shares_outstanding = safe_float(assumptions.get('shares_outstanding'))
        
        if not current_fcf or not shares_outstanding:
            return {'error': 'Missing required FCF or shares outstanding data'}
        
        # Validate inputs
        if discount_rate <= terminal_growth:
            return {'error': f'Discount rate ({discount_rate:.1%}) must be greater than terminal growth ({terminal_growth:.1%})'}
        
        # Calculate projected FCF
        projected_fcf = []
        for year in range(1, years + 1):
            fcf = current_fcf * (1 + growth_rate) ** year
            projected_fcf.append(fcf)
        
        # Calculate terminal value
        terminal_fcf = projected_fcf[-1] * (1 + terminal_growth)
        terminal_value = terminal_fcf / (discount_rate - terminal_growth)
        
        # Calculate present values
        pv_fcf = sum([fcf / (1 + discount_rate) ** (i + 1) for i, fcf in enumerate(projected_fcf)])
        pv_terminal = terminal_value / (1 + discount_rate) ** years
        
        # Calculate enterprise value and equity value
        enterprise_value = pv_fcf + pv_terminal
        equity_value = enterprise_value  # Simplified - would subtract net debt in full model
        fair_value_per_share = equity_value / shares_outstanding
        
        return {
            'fair_value_per_share': fair_value_per_share,
            'enterprise_value': enterprise_value,
            'pv_fcf': pv_fcf,
            'pv_terminal': pv_terminal,
            'terminal_value': terminal_value,
            'projected_fcf': projected_fcf,
            'assumptions': assumptions
        }
        
    except Exception as e:
        current_app.logger.error(f"Error in DCF calculation for {ticker}: {e}")
        return {'error': str(e)}


def get_dcf_sensitivity_data(ticker):
    """
    Generate DCF sensitivity analysis data.
    
    Args:
        ticker (str): Stock ticker symbol
        
    Returns:
        dict: Sensitivity analysis matrix
    """
    try:
        # Get base assumptions (would typically come from fundamentals)
        base_assumptions = {
            'current_fcf': 1000000000,  # $1B base FCF
            'shares_outstanding': 100000000,  # 100M shares
            'years': 10
        }
        
        # Define sensitivity ranges
        growth_rates = [0.02, 0.04, 0.06, 0.08, 0.10]
        discount_rates = [0.08, 0.09, 0.10, 0.11, 0.12]
        terminal_growth = 0.025
        
        sensitivity_matrix = {}
        
        for growth in growth_rates:
            sensitivity_matrix[f"{growth:.0%}"] = {}
            for discount in discount_rates:
                assumptions = base_assumptions.copy()
                assumptions.update({
                    'growth_rate': growth,
                    'discount_rate': discount,
                    'terminal_growth': terminal_growth
                })
                
                result = calculate_dcf(ticker, assumptions)
                if 'error' not in result:
                    fair_value = result['fair_value_per_share']
                    sensitivity_matrix[f"{growth:.0%}"][f"{discount:.0%}"] = fair_value
                else:
                    sensitivity_matrix[f"{growth:.0%}"][f"{discount:.0%}"] = None
        
        return {
            'matrix': sensitivity_matrix,
            'growth_rates': [f"{g:.0%}" for g in growth_rates],
            'discount_rates': [f"{d:.0%}" for d in discount_rates],
            'terminal_growth': f"{terminal_growth:.1%}"
        }
        
    except Exception as e:
        current_app.logger.error(f"Error generating DCF sensitivity data for {ticker}: {e}")
        return {'error': str(e)}


def save_dcf_analysis(analysis):
    """
    Save DCF analysis to session.
    
    Args:
        analysis (dict): DCF analysis results
        
    Returns:
        dict: Success/error status
    """
    try:
        if 'dcf_history' not in session:
            session['dcf_history'] = []
        
        # Add timestamp
        from datetime import datetime
        analysis['timestamp'] = datetime.now().isoformat()
        
        # Add to history (keep last 50 analyses)
        session['dcf_history'].insert(0, analysis)
        session['dcf_history'] = session['dcf_history'][:50]
        session.modified = True
        
        return {'success': True, 'message': 'DCF analysis saved successfully'}
        
    except Exception as e:
        current_app.logger.error(f"Error saving DCF analysis: {e}")
        return {'success': False, 'error': str(e)}


def get_dcf_history(search_query=None):
    """
    Get DCF analysis history with optional search.
    
    Args:
        search_query (str): Optional search query to filter results
        
    Returns:
        list: DCF analysis history
    """
    try:
        history = session.get('dcf_history', [])
        
        if search_query:
            search_query = search_query.lower()
            filtered_history = []
            for analysis in history:
                # Search in ticker, company name, or other relevant fields
                if (search_query in analysis.get('ticker', '').lower() or
                    search_query in analysis.get('company_name', '').lower()):
                    filtered_history.append(analysis)
            return filtered_history
        
        return history
        
    except Exception as e:
        current_app.logger.error(f"Error getting DCF history: {e}")
        return []


def calculate_wacc(fundamentals, price_data, market_return=0.09):
    """
    Calculate Weighted Average Cost of Capital (WACC).
    
    Args:
        fundamentals (dict): Company fundamental data
        price_data (dict): Current price data
        market_return (float): Market return assumption
        
    Returns:
        tuple: (wacc, wacc_inputs, error_message)
    """
    try:
        # Get financial data
        total_debt = safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.longTermDebt"), 0) + \
                    safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.shortTermDebt"), 0)
        
        market_cap = safe_float(get_metric_value_from_path(fundamentals, "Highlights.MarketCapitalization"))
        interest_expense = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.interestExpense"))
        tax_rate = safe_float(get_metric_value_from_path(fundamentals, "Highlights.EffectiveTaxRate"), 25) / 100
        
        if not market_cap or market_cap <= 0:
            return None, None, "Market capitalization data missing or invalid"
        
        # Calculate cost of debt
        cost_of_debt = interest_expense / total_debt if total_debt > 0 and interest_expense else 0.04  # Default 4%
        
        # Calculate cost of equity using CAPM (simplified)
        beta = safe_float(get_metric_value_from_path(fundamentals, "Highlights.Beta"), 1.0)
        risk_free_rate = 0.03  # Assume 3% risk-free rate
        cost_of_equity = risk_free_rate + beta * (market_return - risk_free_rate)
        
        # Calculate weights
        total_capital = market_cap + total_debt
        weight_equity = market_cap / total_capital if total_capital > 0 else 1.0
        weight_debt = total_debt / total_capital if total_capital > 0 else 0.0
        
        # Calculate WACC
        wacc = (weight_equity * cost_of_equity) + (weight_debt * cost_of_debt * (1 - tax_rate))
        
        wacc_inputs = {
            'cost_of_equity': cost_of_equity,
            'cost_of_debt': cost_of_debt,
            'weight_equity': weight_equity,
            'weight_debt': weight_debt,
            'tax_rate': tax_rate,
            'beta': beta,
            'market_cap': market_cap,
            'total_debt': total_debt
        }
        
        return wacc, wacc_inputs, None
        
    except Exception as e:
        current_app.logger.error(f"Error calculating WACC: {e}")
        return None, None, str(e)


def estimate_growth_rate(fundamentals, years=5):
    """
    Estimate growth rate based on historical data and fundamentals.
    
    Args:
        fundamentals (dict): Company fundamental data
        years (int): Number of years for growth estimation
        
    Returns:
        tuple: (growth_rate, growth_source)
    """
    try:
        # Try to get historical revenue growth
        revenue_growth = safe_float(get_metric_value_from_path(fundamentals, "Highlights.QuarterlyRevenueGrowthYOY"))
        
        if revenue_growth:
            # Convert percentage to decimal if needed
            if revenue_growth > 1:
                revenue_growth = revenue_growth / 100
            
            # Cap growth rate at reasonable levels
            growth_rate = min(max(revenue_growth, -0.10), 0.20)  # Between -10% and 20%
            return growth_rate, "Historical Revenue Growth"
        
        # Fallback to ROE-based growth estimate
        roe = safe_float(get_metric_value_from_path(fundamentals, "Highlights.ReturnOnEquityTTM"))
        retention_ratio = 0.7  # Assume 70% retention ratio
        
        if roe:
            if roe > 1:
                roe = roe / 100
            growth_rate = roe * retention_ratio
            growth_rate = min(max(growth_rate, 0), 0.15)  # Between 0% and 15%
            return growth_rate, "ROE-based Estimate"
        
        # Default conservative growth rate
        return 0.05, "Default Conservative Rate"
        
    except Exception as e:
        current_app.logger.error(f"Error estimating growth rate: {e}")
        return 0.05, "Default (Error in Calculation)"


def determine_margin_of_safety(fundamentals):
    """
    Determine appropriate margin of safety based on company quality.
    
    Args:
        fundamentals (dict): Company fundamental data
        
    Returns:
        tuple: (margin_of_safety_percent, reason)
    """
    try:
        # Get quality indicators
        debt_to_equity = safe_float(get_metric_value_from_path(fundamentals, "Highlights.DebtToEquity"), 0)
        current_ratio = safe_float(get_metric_value_from_path(fundamentals, "Highlights.CurrentRatio"), 1)
        roe = safe_float(get_metric_value_from_path(fundamentals, "Highlights.ReturnOnEquityTTM"), 0)
        
        # Convert percentages if needed
        if roe > 1:
            roe = roe / 100
        
        # Score the company (0-100)
        quality_score = 0
        
        # ROE scoring (0-30 points)
        if roe >= 0.15:
            quality_score += 30
        elif roe >= 0.10:
            quality_score += 20
        elif roe >= 0.05:
            quality_score += 10
        
        # Debt scoring (0-30 points)
        if debt_to_equity <= 0.3:
            quality_score += 30
        elif debt_to_equity <= 0.6:
            quality_score += 20
        elif debt_to_equity <= 1.0:
            quality_score += 10
        
        # Liquidity scoring (0-20 points)
        if current_ratio >= 2.0:
            quality_score += 20
        elif current_ratio >= 1.5:
            quality_score += 15
        elif current_ratio >= 1.0:
            quality_score += 10
        
        # Additional 20 points for other factors (simplified)
        quality_score += 20
        
        # Determine margin of safety based on quality
        if quality_score >= 80:
            return 15, "High Quality Company (Low Risk)"
        elif quality_score >= 60:
            return 25, "Medium Quality Company (Moderate Risk)"
        else:
            return 40, "Lower Quality Company (Higher Risk)"
            
    except Exception as e:
        current_app.logger.error(f"Error determining margin of safety: {e}")
        return 25, "Default (Error in Analysis)"