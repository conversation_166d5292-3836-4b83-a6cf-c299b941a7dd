# -*- coding: utf-8 -*-
"""
Financial Calculator Service

This module contains all financial calculation functions and metrics computation.
"""

from flask import current_app
from ..utils.formatters import safe_float
from ..utils.helpers import (
    get_metric_value_from_path, get_industry_benchmark, 
    get_metric_percentile_ranking, format_metric_with_peer_context
)


def calculate_comprehensive_financial_metrics(fundamentals, ticker="", include_peer_context=True):
    """
    Calculate all possible financial metrics from EODHD fundamental data with peer benchmarking.

    Args:
        fundamentals (dict): Company fundamental data
        ticker (str): Company ticker for logging
        include_peer_context (bool): Whether to include industry benchmark comparison

    Returns:
        dict: Comprehensive financial metrics with peer context
    """
    try:
        metrics = {
            'ticker': ticker,
            'profitability': {},
            'efficiency': {},
            'leverage': {},
            'liquidity': {},
            'valuation': {},
            'growth': {},
            'quality': {}
        }

        # Get sector for benchmarking
        sector = get_metric_value_from_path(fundamentals, "General.Sector")

        # --- PROFITABILITY METRICS ---
        # Basic margins
        gross_margin = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.grossProfit"), None)
        revenue = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.totalRevenue"), None)

        if gross_margin and revenue:
            gross_margin_pct = gross_margin / revenue
            benchmark = get_industry_benchmark(sector, 'gross_margin')
            metrics['profitability']['gross_margin'] = {
                'value': gross_margin_pct,
                'formatted': format_metric_with_peer_context(gross_margin_pct, benchmark, 'gross_margin', True) if include_peer_context else f"{gross_margin_pct:.2%}",
                'percentile': get_metric_percentile_ranking(gross_margin_pct, benchmark) if include_peer_context else None
            }

        # Operating margin
        operating_income = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.operatingIncome"), None)
        if operating_income and revenue:
            operating_margin_pct = operating_income / revenue
            benchmark = get_industry_benchmark(sector, 'operating_margin')
            metrics['profitability']['operating_margin'] = {
                'value': operating_margin_pct,
                'formatted': format_metric_with_peer_context(operating_margin_pct, benchmark, 'operating_margin', True) if include_peer_context else f"{operating_margin_pct:.2%}",
                'percentile': get_metric_percentile_ranking(operating_margin_pct, benchmark) if include_peer_context else None
            }

        # Net margin
        net_income = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.netIncome"), None)
        if net_income and revenue:
            net_margin_pct = net_income / revenue
            benchmark = get_industry_benchmark(sector, 'net_margin')
            metrics['profitability']['net_margin'] = {
                'value': net_margin_pct,
                'formatted': format_metric_with_peer_context(net_margin_pct, benchmark, 'net_margin', True) if include_peer_context else f"{net_margin_pct:.2%}",
                'percentile': get_metric_percentile_ranking(net_margin_pct, benchmark) if include_peer_context else None
            }

        # --- EFFICIENCY METRICS ---
        # ROIC
        roic_data = _calculate_roic_from_fundamentals(fundamentals, ticker, include_peer_context)
        if roic_data:
            metrics['efficiency']['roic'] = roic_data if include_peer_context else {'value': roic_data, 'formatted': f"{roic_data:.2%}"}

        # ROE
        roe = safe_float(get_metric_value_from_path(fundamentals, "Highlights.ReturnOnEquityTTM"), None)
        if roe:
            roe_decimal = roe / 100 if roe > 1 else roe
            benchmark = get_industry_benchmark(sector, 'return_on_equity')
            metrics['efficiency']['roe'] = {
                'value': roe_decimal,
                'formatted': format_metric_with_peer_context(roe_decimal, benchmark, 'return_on_equity', True) if include_peer_context else f"{roe_decimal:.2%}",
                'percentile': get_metric_percentile_ranking(roe_decimal, benchmark) if include_peer_context else None
            }

        # ROA
        roa = safe_float(get_metric_value_from_path(fundamentals, "Highlights.ReturnOnAssetsTTM"), None)
        if roa:
            roa_decimal = roa / 100 if roa > 1 else roa
            benchmark = get_industry_benchmark(sector, 'return_on_assets')
            metrics['efficiency']['roa'] = {
                'value': roa_decimal,
                'formatted': format_metric_with_peer_context(roa_decimal, benchmark, 'return_on_assets', True) if include_peer_context else f"{roa_decimal:.2%}",
                'percentile': get_metric_percentile_ranking(roa_decimal, benchmark) if include_peer_context else None
            }

        # --- LEVERAGE METRICS ---
        # Debt-to-Equity
        total_debt = safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.shortLongTermDebtTotal"), 0) + \
                    safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.longTermDebtTotal"), 0)
        total_equity = safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.totalStockholderEquity"), None)

        if total_equity and total_equity != 0:
            de_ratio = total_debt / total_equity
            benchmark = get_industry_benchmark(sector, 'debt_to_equity')
            metrics['leverage']['debt_to_equity'] = {
                'value': de_ratio,
                'formatted': format_metric_with_peer_context(de_ratio, benchmark, 'debt_to_equity', False) if include_peer_context else f"{de_ratio:.2f}",
                'percentile': get_metric_percentile_ranking(de_ratio, benchmark) if include_peer_context else None
            }

        # --- LIQUIDITY METRICS ---
        # Current Ratio
        current_assets = safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.totalCurrentAssets"), None)
        current_liabilities = safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.totalCurrentLiabilities"), None)

        if current_assets and current_liabilities and current_liabilities != 0:
            current_ratio = current_assets / current_liabilities
            benchmark = get_industry_benchmark(sector, 'current_ratio')
            metrics['liquidity']['current_ratio'] = {
                'value': current_ratio,
                'formatted': format_metric_with_peer_context(current_ratio, benchmark, 'current_ratio', True) if include_peer_context else f"{current_ratio:.2f}",
                'percentile': get_metric_percentile_ranking(current_ratio, benchmark) if include_peer_context else None
            }

        # --- VALUATION METRICS ---
        # P/FCF Ratio
        market_cap = safe_float(get_metric_value_from_path(fundamentals, "Highlights.MarketCapitalization"), None)
        fcf = safe_float(get_metric_value_from_path(fundamentals, "Financials.Cash_Flow.yearly.latest.freeCashFlow"), None)

        if market_cap and fcf and fcf > 0:
            pfcf_ratio = market_cap / fcf
            benchmark = get_industry_benchmark(sector, 'p_fcf_ratio')
            metrics['valuation']['p_fcf_ratio'] = {
                'value': pfcf_ratio,
                'formatted': format_metric_with_peer_context(pfcf_ratio, benchmark, 'p_fcf_ratio', False) if include_peer_context else f"{pfcf_ratio:.2f}",
                'percentile': get_metric_percentile_ranking(pfcf_ratio, benchmark) if include_peer_context else None
            }

        # FCF Yield
        if market_cap and fcf:
            fcf_yield = fcf / market_cap
            benchmark = get_industry_benchmark(sector, 'fcf_yield')
            metrics['valuation']['fcf_yield'] = {
                'value': fcf_yield,
                'formatted': format_metric_with_peer_context(fcf_yield, benchmark, 'fcf_yield', True) if include_peer_context else f"{fcf_yield:.2%}",
                'percentile': get_metric_percentile_ranking(fcf_yield, benchmark) if include_peer_context else None
            }

        # P/E Ratio
        pe_ratio = safe_float(get_metric_value_from_path(fundamentals, "Valuation.PERatio"), None)
        if pe_ratio:
            benchmark = get_industry_benchmark(sector, 'p_e_ratio')
            metrics['valuation']['pe_ratio'] = {
                'value': pe_ratio,
                'formatted': format_metric_with_peer_context(pe_ratio, benchmark, 'p_e_ratio', False) if include_peer_context else f"{pe_ratio:.2f}",
                'percentile': get_metric_percentile_ranking(pe_ratio, benchmark) if include_peer_context else None
            }

        # --- GROWTH METRICS ---
        # Revenue Growth
        revenue_growth = safe_float(get_metric_value_from_path(fundamentals, "Highlights.QuarterlyRevenueGrowthYOY"), None)
        if revenue_growth:
            revenue_growth_decimal = revenue_growth / 100 if revenue_growth > 1 else revenue_growth
            benchmark = get_industry_benchmark(sector, 'revenue_growth_5yr')
            metrics['growth']['revenue_growth_yoy'] = {
                'value': revenue_growth_decimal,
                'formatted': format_metric_with_peer_context(revenue_growth_decimal, benchmark, 'revenue_growth_5yr', True) if include_peer_context else f"{revenue_growth_decimal:.2%}",
                'percentile': get_metric_percentile_ranking(revenue_growth_decimal, benchmark) if include_peer_context else None
            }

        return metrics

    except Exception as e:
        if current_app:
            current_app.logger.warning(f"Error calculating comprehensive metrics for {ticker}: {e}")
        return {'error': str(e), 'ticker': ticker}


def calculate_triangulated_valuation(fundamentals, ticker="", current_price=None):
    """
    Calculate comprehensive valuation using multiple methodologies for triangulation.

    Args:
        fundamentals (dict): Company fundamental data
        ticker (str): Company ticker for logging
        current_price (float): Current stock price

    Returns:
        dict: Comprehensive valuation analysis with multiple methods
    """
    try:
        valuation_results = {
            'ticker': ticker,
            'current_price': current_price,
            'dcf_analysis': {},
            'peer_multiples': {},
            'asset_based': {},
            'market_implied': {},
            'historical_context': {},
            'summary': {}
        }

        # --- DCF Analysis ---
        fcf = safe_float(get_metric_value_from_path(fundamentals, "Financials.Cash_Flow.yearly.latest.freeCashFlow"), None)
        revenue = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.totalRevenue"), None)
        shares_outstanding = safe_float(get_metric_value_from_path(fundamentals, "SharesStats.SharesOutstanding"), None)

        if fcf and shares_outstanding:
            fcf_per_share = fcf / shares_outstanding

            # Multiple DCF scenarios
            scenarios = {
                'conservative': {'growth': 0.03, 'terminal': 0.025, 'discount': 0.10},
                'base': {'growth': 0.06, 'terminal': 0.03, 'discount': 0.09},
                'optimistic': {'growth': 0.10, 'terminal': 0.035, 'discount': 0.08}
            }

            dcf_values = {}
            for scenario_name, params in scenarios.items():
                # Simple DCF calculation (5-year projection + terminal value)
                projected_fcf = []
                for year in range(1, 6):
                    projected_fcf.append(fcf_per_share * (1 + params['growth']) ** year)

                # Terminal value
                terminal_fcf = projected_fcf[-1] * (1 + params['terminal'])
                terminal_value = terminal_fcf / (params['discount'] - params['terminal'])

                # Present value calculation
                pv_fcf = sum([cf / (1 + params['discount']) ** (i + 1) for i, cf in enumerate(projected_fcf)])
                pv_terminal = terminal_value / (1 + params['discount']) ** 5

                dcf_values[scenario_name] = pv_fcf + pv_terminal

            valuation_results['dcf_analysis'] = {
                'fcf_per_share': fcf_per_share,
                'scenarios': dcf_values,
                'range': f"${min(dcf_values.values()):.2f} - ${max(dcf_values.values()):.2f}",
                'midpoint': sum(dcf_values.values()) / len(dcf_values)
            }

        # --- Peer Multiple Analysis ---
        sector = get_metric_value_from_path(fundamentals, "General.Sector")
        pe_ratio = safe_float(get_metric_value_from_path(fundamentals, "Valuation.PERatio"), None)
        ev_ebitda = safe_float(get_metric_value_from_path(fundamentals, "Valuation.EVToEBITDA"), None)
        price_to_sales = safe_float(get_metric_value_from_path(fundamentals, "Valuation.PriceToSalesRatio"), None)

        # Get industry benchmarks
        industry_pe = get_industry_benchmark(sector, 'p_e_ratio')
        industry_pfcf = get_industry_benchmark(sector, 'p_fcf_ratio')

        if pe_ratio and industry_pe and current_price:
            current_eps = current_price / pe_ratio if pe_ratio else None
            peer_multiple_values = {}

            if current_eps and industry_pe:
                peer_multiple_values['pe_based'] = current_eps * industry_pe

            if fcf_per_share and industry_pfcf:
                peer_multiple_values['pfcf_based'] = fcf_per_share * industry_pfcf

            valuation_results['peer_multiples'] = {
                'current_multiples': {
                    'pe': pe_ratio,
                    'ev_ebitda': ev_ebitda,
                    'price_to_sales': price_to_sales
                },
                'industry_benchmarks': {
                    'pe': industry_pe,
                    'pfcf': industry_pfcf
                },
                'implied_values': peer_multiple_values,
                'relative_valuation': 'Premium' if pe_ratio and industry_pe and pe_ratio > industry_pe else 'Discount'
            }

        # --- Summary and Recommendation ---
        all_values = []

        # Collect all valuation estimates
        if 'scenarios' in valuation_results['dcf_analysis']:
            all_values.extend(valuation_results['dcf_analysis']['scenarios'].values())

        if 'implied_values' in valuation_results['peer_multiples']:
            all_values.extend(valuation_results['peer_multiples']['implied_values'].values())

        if all_values:
            valuation_range = {
                'low': min(all_values),
                'high': max(all_values),
                'median': sorted(all_values)[len(all_values)//2],
                'mean': sum(all_values) / len(all_values)
            }

            # Calculate upside/downside
            if current_price:
                upside_potential = (valuation_range['median'] - current_price) / current_price

                valuation_results['summary'] = {
                    'valuation_range': valuation_range,
                    'current_price': current_price,
                    'upside_potential': upside_potential,
                    'recommendation': 'Buy' if upside_potential > 0.15 else 'Hold' if upside_potential > -0.10 else 'Sell',
                    'confidence_level': 'High' if len(all_values) >= 4 else 'Medium' if len(all_values) >= 2 else 'Low'
                }

        return valuation_results

    except Exception as e:
        if current_app:
            current_app.logger.warning(f"Error in triangulated valuation for {ticker}: {e}")
        return {'error': str(e), 'ticker': ticker}


def _calculate_roic_from_fundamentals(fundamentals, ticker="", include_peer_context=False):
    """
    Calculate Return on Invested Capital (ROIC) from fundamentals data with optional peer benchmarking.
    """
    try:
        # Use EBIT for operating income
        ebit = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.ebit"), None)
        if ebit is None:
            ebit = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.operatingIncome"), None)

        # Calculate after-tax operating income (NOPAT)
        tax_rate = 0.25  # Default corporate tax rate
        income_tax = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.incomeTaxExpense"), None)
        income_before_tax = safe_float(get_metric_value_from_path(fundamentals, "Financials.Income_Statement.yearly.latest.incomeBeforeTax"), None)

        if income_tax is not None and income_before_tax is not None and income_before_tax != 0:
            tax_rate = income_tax / income_before_tax

        nopat = ebit * (1 - tax_rate) if ebit is not None else None

        # Calculate invested capital (Total Assets - Current Liabilities)
        total_assets = safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.totalAssets"), None)
        current_liabilities = safe_float(get_metric_value_from_path(fundamentals, "Financials.Balance_Sheet.yearly.latest.totalCurrentLiabilities"), None)

        if nopat is not None and total_assets is not None and current_liabilities is not None:
            invested_capital = total_assets - current_liabilities
            if invested_capital != 0:
                roic = nopat / invested_capital
                if current_app:
                    current_app.logger.debug(f"ROIC calculated for {ticker}: {roic:.4f} ({roic*100:.2f}%)")

                if include_peer_context:
                    sector = get_metric_value_from_path(fundamentals, "General.Sector")
                    benchmark_roic = get_industry_benchmark(sector, 'roic')

                    return {
                        'value': roic,
                        'formatted': format_metric_with_peer_context(roic, benchmark_roic, 'roic', True),
                        'percentile': get_metric_percentile_ranking(roic, benchmark_roic),
                        'benchmark': benchmark_roic,
                        'sector': sector
                    }

                return roic

        if current_app:
            current_app.logger.debug(f"ROIC calculation failed for {ticker}")
        return None if not include_peer_context else {'value': None, 'formatted': 'N/A', 'percentile': 'N/A'}
    except Exception as e:
        if current_app:
            current_app.logger.warning(f"Error calculating ROIC for {ticker}: {e}")
        return None