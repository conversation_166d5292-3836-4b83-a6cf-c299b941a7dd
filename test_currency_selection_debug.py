#!/usr/bin/env python3
"""
Debug script to test why currency selection modal isn't appearing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService

def test_dkk_currency_selection():
    """Test DKK currency to see if selection modal should appear."""
    print("🔍 DEBUGGING DKK CURRENCY SELECTION")
    print("=" * 50)
    
    # DKK portfolio that should trigger currency selection
    dkk_portfolio_text = """
    Min Aktieportefølje
    
    Ticker    Markedsværdi    GAK        Antal
    AAPL      2.462,85 DKK    161,61 kr  13 stk
    MSFT      3.250,75 DKK    325,08 kr  10 stk
    NOVO      1.890,50 DKK    189,05 kr  10 stk
    """
    
    service = PortfolioImportService("demo_key", "demo_key")
    result = service.extract_portfolio_from_text(dkk_portfolio_text)
    api_response = service.format_for_api(result)
    
    print("📊 API RESPONSE ANALYSIS:")
    print(f"   Success: {api_response.get('success', False)}")
    print(f"   Detected Currency: {api_response.get('detected_currency', 'None')}")
    print(f"   Portfolio Entries: {len(api_response.get('portfolio', []))}")
    
    currency_info = api_response.get('currency_info', {})
    print(f"\n💱 CURRENCY INFO:")
    print(f"   Detected Currencies: {currency_info.get('detected_currencies', [])}")
    print(f"   Primary Currency: {currency_info.get('primary_currency', 'None')}")
    print(f"   Has Mixed Currencies: {currency_info.get('has_mixed_currencies', False)}")
    print(f"   Requires User Selection: {currency_info.get('requires_user_selection', False)}")
    print(f"   Selection Reason: {currency_info.get('currency_selection_reason', 'None')}")
    
    uncertainties = currency_info.get('currency_uncertainties', [])
    print(f"\n🤔 CURRENCY UNCERTAINTIES: {len(uncertainties)}")
    for i, uncertainty in enumerate(uncertainties, 1):
        print(f"   {i}. Type: {uncertainty.get('type', 'unknown')}")
        print(f"      Reason: {uncertainty.get('reason', 'No reason')}")
        print(f"      Confidence: {uncertainty.get('confidence', 'unknown')}")
    
    gemini_question = currency_info.get('gemini_question')
    if gemini_question:
        print(f"\n🤖 GEMINI QUESTION:")
        print(f"   {gemini_question}")
    else:
        print(f"\n🤖 GEMINI QUESTION: None generated")
    
    # Check what should happen
    should_show_modal = currency_info.get('requires_user_selection', False)
    print(f"\n🎯 MODAL BEHAVIOR:")
    if should_show_modal:
        print("   ✅ Currency selection modal SHOULD appear")
        print("   ✅ User will be asked to select currency")
    else:
        print("   ❌ Currency selection modal will NOT appear")
        print("   ❌ Will use default currency")
    
    return api_response

def test_mixed_currency_selection():
    """Test mixed currency portfolio."""
    print("\n\n🌍 DEBUGGING MIXED CURRENCY SELECTION")
    print("=" * 50)
    
    mixed_portfolio_text = """
    International Portfolio
    
    AAPL    $2,500.00 USD    $125.00    20 shares
    ASML    €1,800.00 EUR    €180.00    10 shares  
    NOVO    1,200.00 DKK     150.00 kr  8 shares
    """
    
    service = PortfolioImportService("demo_key", "demo_key")
    result = service.extract_portfolio_from_text(mixed_portfolio_text)
    api_response = service.format_for_api(result)
    
    currency_info = api_response.get('currency_info', {})
    print(f"💱 MIXED CURRENCY ANALYSIS:")
    print(f"   Detected Currencies: {currency_info.get('detected_currencies', [])}")
    print(f"   Requires User Selection: {currency_info.get('requires_user_selection', False)}")
    print(f"   Mixed Currency Entries: {len(currency_info.get('mixed_currency_entries', []))}")
    
    gemini_question = currency_info.get('gemini_question')
    if gemini_question:
        print(f"\n🤖 GEMINI QUESTION:")
        print(f"   {gemini_question}")
    
    return api_response

def test_usd_default_issue():
    """Test why it's defaulting to USD."""
    print("\n\n💵 DEBUGGING USD DEFAULT ISSUE")
    print("=" * 50)
    
    # Simple DKK text
    simple_dkk_text = "AAPL 1000 kr"
    
    service = PortfolioImportService("demo_key", "demo_key")
    result = service.extract_portfolio_from_text(simple_dkk_text)
    api_response = service.format_for_api(result)
    
    print(f"📊 SIMPLE DKK TEST:")
    print(f"   Input: '{simple_dkk_text}'")
    print(f"   Detected Currency: {api_response.get('detected_currency', 'None')}")
    print(f"   Primary Currency: {api_response.get('currency_info', {}).get('primary_currency', 'None')}")
    print(f"   Requires Selection: {api_response.get('currency_info', {}).get('requires_user_selection', False)}")
    
    # Check individual entries
    portfolio = api_response.get('portfolio', [])
    if portfolio:
        entry = portfolio[0]
        print(f"   Entry Currency: {entry.get('currency', 'None')}")
        print(f"   Buy Price Currency: {entry.get('buy_price_currency', 'None')}")
    
    return api_response

def main():
    """Run all debug tests."""
    print("🚨 CURRENCY SELECTION DEBUG SUITE")
    print("=" * 60)
    print("Investigating why currency selection modal isn't appearing...")
    print("=" * 60)
    
    try:
        # Test 1: DKK currency selection
        dkk_result = test_dkk_currency_selection()
        
        # Test 2: Mixed currency selection
        mixed_result = test_mixed_currency_selection()
        
        # Test 3: USD default issue
        usd_result = test_usd_default_issue()
        
        print("\n" + "=" * 60)
        print("🎯 DEBUG SUMMARY")
        print("=" * 60)
        
        # Analyze results
        dkk_should_show = dkk_result.get('currency_info', {}).get('requires_user_selection', False)
        mixed_should_show = mixed_result.get('currency_info', {}).get('requires_user_selection', False)
        
        print(f"DKK Portfolio Modal: {'✅ SHOULD SHOW' if dkk_should_show else '❌ NOT SHOWING'}")
        print(f"Mixed Currency Modal: {'✅ SHOULD SHOW' if mixed_should_show else '❌ NOT SHOWING'}")
        
        if not dkk_should_show:
            print("\n🔧 ISSUE IDENTIFIED:")
            print("   DKK currency selection is not being triggered")
            print("   Need to fix currency uncertainty detection")
        
        if not mixed_should_show:
            print("\n🔧 ISSUE IDENTIFIED:")
            print("   Mixed currency selection is not being triggered")
            print("   Need to fix mixed currency detection")
        
        # Check for Gemini questions
        dkk_question = dkk_result.get('currency_info', {}).get('gemini_question')
        mixed_question = mixed_result.get('currency_info', {}).get('gemini_question')
        
        print(f"\nGemini Questions Generated:")
        print(f"   DKK: {'✅ YES' if dkk_question else '❌ NO'}")
        print(f"   Mixed: {'✅ YES' if mixed_question else '❌ NO'}")
        
    except Exception as e:
        print(f"\n❌ Debug failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()