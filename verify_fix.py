#!/usr/bin/env python3

print("🧪 Testing Currency Fix")
print("=" * 40)

try:
    from portfolio_import import AIPortfolioExtractor
    print("✅ Import successful")
    
    # Create extractor
    extractor = AIPortfolioExtractor("test_key", "test_eodhd_key")
    extractor.user_portfolio_currency = "USD"
    print(f"✅ User portfolio currency: {extractor.user_portfolio_currency}")
    
    # Test data with DKK (simulating AI detection)
    test_data = [{
        'ticker': 'GOOGL',
        'amount_invested': 15000,  # 15,000 DKK
        'amount_invested_currency': 'DKK',
        'buy_price': 830,  # 830 DKK per share
        'buy_price_currency': 'DKK',
        'shares': 0
    }]
    
    print("📊 Input data:")
    print(f"  Amount: {test_data[0]['amount_invested']} {test_data[0]['amount_invested_currency']}")
    print(f"  Buy Price: {test_data[0]['buy_price']} {test_data[0]['buy_price_currency']}")
    
    # Process
    result = extractor._process_gemini_response(test_data)
    
    if result:
        entry = result[0]
        print("\n📊 Output:")
        print(f"  Shares: {entry['shares']:.6f}")
        print(f"  Amount: {entry['amount_invested']} {entry.get('amount_invested_currency', 'N/A')}")
        print(f"  Buy Price: {entry['buy_price']} {entry.get('buy_price_currency', 'N/A')}")
        
        # Expected shares: 15000 / 830 = ~18.07
        expected = 15000 / 830
        print(f"  Expected shares: {expected:.6f}")
        print(f"  Difference: {abs(entry['shares'] - expected):.6f}")
        
        # Check if currency is preserved
        if entry.get('amount_invested_currency') == 'DKK':
            print("✅ Currency preserved correctly!")
        else:
            print(f"❌ Currency not preserved: {entry.get('amount_invested_currency')}")
            
        # Check if amounts are reasonable
        if 10000 <= entry['amount_invested'] <= 20000:
            print("✅ Amount scale is reasonable!")
        else:
            print(f"❌ Amount scale seems wrong: {entry['amount_invested']}")
            
        # Check share calculation
        if abs(entry['shares'] - expected) < 0.01:
            print("✅ Share calculation is correct!")
        else:
            print("❌ Share calculation is incorrect!")
            
    else:
        print("❌ No results returned")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 Test complete!")
