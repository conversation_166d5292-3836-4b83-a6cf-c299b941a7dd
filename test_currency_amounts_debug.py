#!/usr/bin/env python3
"""
Debug script to test why amounts are showing in USD instead of DKK.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService

def test_dkk_amounts_preservation():
    """Test that DKK amounts are preserved without conversion to USD."""
    print("🔍 DEBUGGING DKK AMOUNTS PRESERVATION")
    print("=" * 60)
    
    # DKK portfolio with specific amounts
    dkk_portfolio_text = """
    Min Aktieportefølje
    
    Ticker    Markedsværdi    GAK        Antal
    AAPL      2.462,85 DKK    161,61 kr  13 stk
    MSFT      3.250,75 DKK    325,08 kr  10 stk
    NOVO      1.890,50 DKK    189,05 kr  10 stk
    """
    
    service = PortfolioImportService("demo_key", "demo_key")
    result = service.extract_portfolio_from_text(dkk_portfolio_text)
    api_response = service.format_for_api(result)
    
    print("📊 EXTRACTION RESULTS:")
    print(f"   Success: {api_response.get('success', False)}")
    print(f"   Detected Currency: {api_response.get('detected_currency', 'None')}")
    print(f"   Primary Currency: {api_response.get('currency_info', {}).get('primary_currency', 'None')}")
    
    # Check individual portfolio entries
    portfolio_entries = api_response.get('portfolio', [])
    print(f"\n💰 PORTFOLIO ENTRIES ({len(portfolio_entries)} found):")
    
    for i, entry in enumerate(portfolio_entries, 1):
        ticker = entry.get('ticker', 'Unknown')
        amount_invested = entry.get('amount_invested', 0)
        buy_price = entry.get('buy_price', 0)
        currency = entry.get('currency', 'Unknown')
        buy_currency = entry.get('buy_price_currency', 'Unknown')
        
        print(f"\n   Entry {i}: {ticker}")
        print(f"     Amount Invested: {amount_invested} {currency}")
        print(f"     Buy Price: {buy_price} {buy_currency}")
        print(f"     Shares: {entry.get('shares', 0)}")
        
        # Check if amounts look correct
        if currency == 'DKK' and amount_invested > 100:  # Should be in hundreds for DKK
            print(f"     ✅ Amount looks correct for DKK")
        elif currency == 'USD' and amount_invested < 100:  # Would be much smaller if converted to USD
            print(f"     ❌ Amount looks like it was converted to USD!")
            print(f"     ❌ Expected DKK amounts in hundreds, got USD amounts in tens")
        else:
            print(f"     ⚠️  Amount: {amount_invested} {currency} - needs verification")
    
    # Test user currency selection
    print(f"\n🎯 SIMULATING USER CURRENCY SELECTION:")
    
    # User selects DKK (same as detected)
    user_selected_data = {
        **api_response,
        'selected_currency': 'DKK',
        'user_selected_currency': True
    }
    
    print(f"   User selects: DKK")
    print(f"   Detected currency: {api_response.get('detected_currency')}")
    print(f"   Should preserve original amounts: YES")
    
    # Check what would happen in backend processing
    selected_currency = user_selected_data.get('selected_currency', 'USD')
    source_currency = user_selected_data.get('currency', 'DKK')
    
    print(f"\n🔧 BACKEND PROCESSING SIMULATION:")
    print(f"   Selected Currency: {selected_currency}")
    print(f"   Source Currency: {source_currency}")
    
    # Simulate the conversion logic
    for i, entry in enumerate(portfolio_entries, 1):
        entry_buy_currency = entry.get('buy_price_currency', source_currency)
        amount_invested_original = entry.get('amount_invested', 0)
        
        print(f"\n   Entry {i} ({entry.get('ticker')}) Processing:")
        print(f"     Original Amount: {amount_invested_original} {entry_buy_currency}")
        print(f"     Selected Currency: {selected_currency}")
        
        if entry_buy_currency == selected_currency:
            print(f"     ✅ No conversion needed: {entry_buy_currency} == {selected_currency}")
            print(f"     ✅ Final Amount: {amount_invested_original} {selected_currency}")
        else:
            print(f"     ⚠️  Conversion needed: {entry_buy_currency} -> {selected_currency}")
            # Simulate conversion (this is where the problem might be)
            usd_rate = 0.145  # DKK to USD rate
            if entry_buy_currency == 'DKK':
                usd_amount = amount_invested_original * usd_rate
                print(f"     Converted to USD: {usd_amount}")
                if selected_currency == 'USD':
                    final_amount = usd_amount
                else:
                    # Convert back from USD to selected currency
                    final_amount = usd_amount  # This would use another rate
                print(f"     Final Amount: {final_amount} {selected_currency}")
    
    return api_response

def test_expected_vs_actual():
    """Test what we expect vs what we're getting."""
    print("\n\n📋 EXPECTED VS ACTUAL AMOUNTS")
    print("=" * 60)
    
    print("🎯 EXPECTED BEHAVIOR:")
    print("   User uploads DKK portfolio with amounts like:")
    print("     AAPL: 2,462.85 DKK invested")
    print("     MSFT: 3,250.75 DKK invested")
    print("   User selects DKK currency")
    print("   Portfolio should show:")
    print("     AAPL: 2,462.85 DKK (no conversion)")
    print("     MSFT: 3,250.75 DKK (no conversion)")
    
    print("\n❌ ACTUAL BEHAVIOR (PROBLEM):")
    print("   Same DKK portfolio")
    print("   User selects DKK currency")
    print("   Portfolio shows:")
    print("     AAPL: ~357 USD (converted!)")
    print("     MSFT: ~471 USD (converted!)")
    print("   This is wrong - amounts should stay in DKK!")
    
    print("\n🔧 ROOT CAUSE ANALYSIS:")
    print("   1. Portfolio entries might be created with wrong currency")
    print("   2. Currency conversion logic might be running when it shouldn't")
    print("   3. Backend might not be preserving original amounts")
    print("   4. Frontend might be displaying wrong currency")

def main():
    """Run currency amounts debugging."""
    print("🚨 CURRENCY AMOUNTS DEBUG SUITE")
    print("=" * 70)
    print("Investigating why DKK amounts are showing as USD")
    print("=" * 70)
    
    try:
        # Test DKK amounts preservation
        result = test_dkk_amounts_preservation()
        
        # Test expected vs actual behavior
        test_expected_vs_actual()
        
        print("\n" + "=" * 70)
        print("🎯 DEBUG SUMMARY")
        print("=" * 70)
        
        detected_currency = result.get('detected_currency')
        portfolio_entries = result.get('portfolio', [])
        
        if detected_currency == 'DKK':
            print("✅ Currency detection is working (DKK detected)")
        else:
            print(f"❌ Currency detection issue (detected: {detected_currency})")
        
        # Check if amounts look like DKK or USD
        dkk_amounts = 0
        usd_amounts = 0
        
        for entry in portfolio_entries:
            amount = entry.get('amount_invested', 0)
            currency = entry.get('currency', 'Unknown')
            
            if currency == 'DKK' and amount > 1000:  # DKK amounts should be in thousands
                dkk_amounts += 1
            elif currency == 'USD' and amount < 1000:  # USD amounts would be much smaller
                usd_amounts += 1
        
        print(f"\nAmount Analysis:")
        print(f"   Entries with DKK-sized amounts: {dkk_amounts}")
        print(f"   Entries with USD-sized amounts: {usd_amounts}")
        
        if dkk_amounts > 0 and usd_amounts == 0:
            print("✅ Amounts appear to be in correct DKK scale")
        elif usd_amounts > 0:
            print("❌ Amounts appear to have been converted to USD scale")
            print("🔧 ISSUE: Currency conversion is happening when it shouldn't")
        
    except Exception as e:
        print(f"\n❌ Debug failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()