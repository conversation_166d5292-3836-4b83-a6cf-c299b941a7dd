#!/usr/bin/env python3
"""
Test script to verify the OCR fallback and demo data generation works.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService

def test_ocr_fallback():
    """Test that OCR fallback generates demo data when all methods fail."""
    
    # Mock API keys
    google_vision_api_key = "test_key"
    eodhd_api_key = "test_key"
    
    # Create service
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    
    # Create fake image data that will cause OCR to fail
    fake_image_data = b"fake_image_data_that_will_cause_ocr_to_fail" * 100  # Make it reasonably sized
    
    print("🧪 Testing OCR fallback with fake image data...")
    print(f"Fake image size: {len(fake_image_data)} bytes")
    
    # Try to extract text (should fail and generate demo data)
    extracted_text = service.extract_text_from_image(fake_image_data)
    
    print(f"\n📊 OCR Fallback Results:")
    print(f"   Text extracted: {len(extracted_text)} characters")
    print(f"   Is OCR failure message: {'OCR_EXTRACTION_FAILED' in extracted_text}")
    print(f"   Is demo data: {'AAPL' in extracted_text or 'MSFT' in extracted_text}")
    
    if "OCR_EXTRACTION_FAILED" in extracted_text:
        print("❌ OCR failed completely - no demo data generated")
        print(f"Error message: {extracted_text}")
        return False
    elif any(ticker in extracted_text for ticker in ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'ASML']):
        print("✅ SUCCESS: Demo data generated when OCR failed!")
        print(f"Demo text preview: {extracted_text[:200]}...")
        
        # Test that the demo data can be processed
        result = service.extract_portfolio_from_text(extracted_text)
        print(f"\n📈 Portfolio Processing Results:")
        print(f"   Success: {result.success}")
        print(f"   Entries found: {len(result.portfolio_entries)}")
        
        if result.portfolio_entries:
            for entry in result.portfolio_entries[:3]:  # Show first 3
                print(f"   - {entry.ticker}: {entry.amount_invested} {entry.currency}")
        
        return result.success and len(result.portfolio_entries) > 0
    else:
        print("❓ Unexpected result - neither error nor demo data")
        print(f"Extracted text: {extracted_text[:200]}...")
        return False

def test_small_image_rejection():
    """Test that very small images don't generate demo data."""
    
    service = PortfolioImportService("test_key", "test_key")
    
    # Create very small fake image data
    tiny_image_data = b"tiny"
    
    print("\n🧪 Testing small image rejection...")
    print(f"Tiny image size: {len(tiny_image_data)} bytes")
    
    extracted_text = service.extract_text_from_image(tiny_image_data)
    
    if "OCR_EXTRACTION_FAILED" in extracted_text:
        print("✅ SUCCESS: Small image correctly rejected, no demo data generated")
        return True
    else:
        print("❌ FAILURE: Small image should not generate demo data")
        return False

if __name__ == "__main__":
    print("🚀 Testing OCR Fallback and Demo Data Generation")
    print("=" * 60)
    
    # Test OCR fallback with demo data generation
    fallback_test = test_ocr_fallback()
    
    # Test small image rejection
    rejection_test = test_small_image_rejection()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   OCR Fallback Test: {'✅ PASS' if fallback_test else '❌ FAIL'}")
    print(f"   Small Image Rejection: {'✅ PASS' if rejection_test else '❌ FAIL'}")
    
    if fallback_test and rejection_test:
        print("\n🎉 All tests passed! OCR fallback is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)