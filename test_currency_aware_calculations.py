#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Currency-Aware Share Calculations

This script tests the enhanced currency-aware calculation logic to ensure
shares are calculated correctly based on the actual currency values, not
converted to USD incorrectly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService
import json

def test_dkk_share_calculation():
    """Test DKK share calculation - should not convert to USD."""
    print("🧪 Testing DKK Share Calculation (No USD conversion)")
    print("=" * 70)
    
    dkk_portfolio_text = """
    Portfolio Holdings:
    
    NOVO - Novo Nordisk
    Shares: 100 stk
    Buy Price: 850.50 kr
    Current Price: 920.75 kr
    Current Value: 92,075 kr
    
    ORSTED - Ørsted
    Shares: 50 stk
    Buy Price: 450.25 kr
    Current Price: 485.30 kr
    Current Value: 24,265 kr
    """
    
    try:
        service = PortfolioImportService("test_key", "test_key")
        result = service.extract_portfolio_from_text(dkk_portfolio_text)
        api_response = service.format_for_api(result)
        
        print(f"✅ Extraction completed!")
        print(f"   Success: {api_response.get('success', False)}")
        print(f"   Portfolio entries: {len(api_response.get('portfolio', []))}")
        
        # Check share calculations
        portfolio = api_response.get('portfolio', [])
        for entry in portfolio:
            ticker = entry.get('ticker')
            shares = entry.get('shares', 0)
            buy_price = entry.get('buy_price', 0)
            current_price = entry.get('current_price', 0)
            current_value = entry.get('current_value', 0)
            buy_currency = entry.get('buy_price_currency', 'Unknown')
            current_currency = entry.get('current_value_currency', 'Unknown')
            
            print(f"\n📊 {ticker} Analysis:")
            print(f"   Shares: {shares}")
            print(f"   Buy Price: {buy_price} {buy_currency}")
            print(f"   Current Price: {current_price} {entry.get('current_price_currency', 'Unknown')}")
            print(f"   Current Value: {current_value} {current_currency}")
            
            # Verify calculations (handle None values)
            if shares > 0 and current_price is not None and current_price > 0:
                expected_current_value = shares * current_price
                calculation_error = abs(expected_current_value - current_value) / current_value * 100
                
                print(f"   Expected Current Value: {expected_current_value:.2f}")
                print(f"   Actual Current Value: {current_value}")
                print(f"   Calculation Error: {calculation_error:.2f}%")
                
                if calculation_error < 5:  # Allow 5% tolerance
                    print(f"   ✅ Share calculation CORRECT")
                else:
                    print(f"   ❌ Share calculation INCORRECT")
            
            if shares > 0 and buy_price > 0:
                expected_invested = shares * buy_price
                invested = entry.get('amount_invested', 0)
                if invested > 0:
                    investment_error = abs(expected_invested - invested) / invested * 100
                    print(f"   Investment calculation error: {investment_error:.2f}%")
        
        return api_response
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_mixed_currency_calculation():
    """Test mixed currency calculation - USD buy prices, DKK current values."""
    print("\n🧪 Testing Mixed Currency Calculation (USD buy, DKK current)")
    print("=" * 70)
    
    mixed_currency_text = """
    Portfolio Holdings:
    
    GOOGL - Alphabet Inc
    Shares: 83.26 stk
    GAK: 161.61 USD
    Current Price: 192.06 USD
    Markedsværdi: 15,848 kr
    
    AAPL - Apple Inc
    Shares: 44.64 stk
    GAK: 189.45 USD
    Current Price: 225.30 USD
    Markedsværdi: 10,058 kr
    """
    
    try:
        service = PortfolioImportService("test_key", "test_key")
        result = service.extract_portfolio_from_text(mixed_currency_text)
        api_response = service.format_for_api(result)
        
        print(f"✅ Extraction completed!")
        
        # Check mixed currency handling
        portfolio = api_response.get('portfolio', [])
        for entry in portfolio:
            ticker = entry.get('ticker')
            shares = entry.get('shares', 0)
            buy_price = entry.get('buy_price', 0)
            current_price = entry.get('current_price', 0)
            current_value = entry.get('current_value', 0)
            buy_currency = entry.get('buy_price_currency', 'Unknown')
            current_currency = entry.get('current_value_currency', 'Unknown')
            
            print(f"\n📊 {ticker} Mixed Currency Analysis:")
            print(f"   Shares: {shares}")
            print(f"   Buy Price: {buy_price} {buy_currency}")
            print(f"   Current Price: {current_price} {entry.get('current_price_currency', 'Unknown')}")
            print(f"   Current Value: {current_value} {current_currency}")
            
            # Check if currencies are properly preserved
            if buy_currency != current_currency:
                print(f"   ✅ Mixed currencies properly detected: {buy_currency} → {current_currency}")
            else:
                print(f"   ⚠️  Expected mixed currencies, but got same: {buy_currency}")
            
            # For mixed currencies, shares should be calculated intelligently
            if shares > 0:
                print(f"   ✅ Shares calculated despite mixed currencies")
            else:
                print(f"   ❌ Shares not calculated for mixed currency scenario")
        
        return api_response
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return None

def test_usd_calculation():
    """Test USD calculation - should work normally."""
    print("\n🧪 Testing USD Calculation (Normal scenario)")
    print("=" * 70)
    
    usd_portfolio_text = """
    Portfolio Holdings:
    
    AAPL - Apple Inc
    Shares: 100
    Buy Price: $150.00
    Current Price: $175.50
    Current Value: $17,550.00
    
    MSFT - Microsoft Corp
    Shares: 50
    Buy Price: $300.00
    Current Price: $350.00
    Current Value: $17,500.00
    """
    
    try:
        service = PortfolioImportService("test_key", "test_key")
        result = service.extract_portfolio_from_text(usd_portfolio_text)
        api_response = service.format_for_api(result)
        
        print(f"✅ Extraction completed!")
        
        # Check USD calculations
        portfolio = api_response.get('portfolio', [])
        for entry in portfolio:
            ticker = entry.get('ticker')
            shares = entry.get('shares', 0)
            buy_price = entry.get('buy_price', 0)
            current_price = entry.get('current_price', 0)
            current_value = entry.get('current_value', 0)
            
            print(f"\n📊 {ticker} USD Analysis:")
            print(f"   Shares: {shares}")
            print(f"   Buy Price: ${buy_price}")
            print(f"   Current Price: ${current_price}")
            print(f"   Current Value: ${current_value}")
            
            # Verify USD calculations are exact (handle None values)
            if shares > 0 and current_price is not None and current_price > 0:
                expected_current_value = shares * current_price
                if abs(expected_current_value - current_value) < 0.01:
                    print(f"   ✅ USD calculation PERFECT")
                else:
                    print(f"   ❌ USD calculation error: expected {expected_current_value}, got {current_value}")
        
        return api_response
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return None

def main():
    """Run all currency-aware calculation tests."""
    print("🚀 Currency-Aware Share Calculation Test Suite")
    print("=" * 70)
    
    # Test 1: DKK share calculation
    dkk_result = test_dkk_share_calculation()
    
    # Test 2: Mixed currency calculation
    mixed_result = test_mixed_currency_calculation()
    
    # Test 3: USD calculation
    usd_result = test_usd_calculation()
    
    print("\n📊 Test Summary:")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 3
    
    if dkk_result and dkk_result.get('success'):
        print("✅ DKK currency calculation: PASSED")
        tests_passed += 1
    else:
        print("❌ DKK currency calculation: FAILED")
    
    if mixed_result and mixed_result.get('success'):
        print("✅ Mixed currency calculation: PASSED")
        tests_passed += 1
    else:
        print("❌ Mixed currency calculation: FAILED")
    
    if usd_result and usd_result.get('success'):
        print("✅ USD currency calculation: PASSED")
        tests_passed += 1
    else:
        print("❌ USD currency calculation: FAILED")
    
    print(f"\n🎯 Overall Result: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All currency-aware calculations working perfectly!")
        print("💰 Shares are now calculated correctly based on actual currency values!")
    else:
        print("⚠️  Some currency calculations failed. Please review the implementation.")

if __name__ == "__main__":
    main()
