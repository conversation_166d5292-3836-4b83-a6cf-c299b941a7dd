#!/usr/bin/env python3
"""
Complete test to verify both spreadsheet and image fixes
"""

import io
import csv
from portfolio_import import process_spreadsheet_upload, process_image_upload

def test_spreadsheet_fix():
    """Test that spreadsheet processing works correctly"""
    print("🔍 Testing Spreadsheet Processing")
    print("=" * 40)
    
    # Create test CSV
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(['Ticker', 'Amount Invested', 'Buy Price', 'Purchase Date'])
    writer.writerow(['AAPL', '5000.00', '175.25', '2024-01-15'])
    writer.writerow(['MSFT', '3000.00', '380.50', '2024-02-10'])
    
    csv_content = output.getvalue().encode('utf-8')
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_content, 'test.csv', google_vision_api_key)
    
    print(f"Success: {result['success']}")
    print(f"Entries: {len(result.get('portfolio', []))}")
    
    # Check for fake data
    has_fake_data = False
    if result.get('portfolio'):
        for entry in result['portfolio']:
            if (entry.get('amount_invested') in [2554.80, 2137.50] or 
                entry.get('buy_price') in [425.80, 142.50]):
                has_fake_data = True
                break
        
        print("Portfolio data:")
        for entry in result['portfolio']:
            print(f"  {entry['ticker']}: ${entry['amount_invested']} @ ${entry['buy_price']}")
    
    if result['success'] and not has_fake_data:
        print("✅ Spreadsheet processing works correctly!")
        return True
    else:
        print("❌ Spreadsheet processing has issues!")
        return False

def test_image_fix():
    """Test that image processing no longer returns fake data"""
    print("\n🔍 Testing Image Processing")
    print("=" * 40)
    
    # Test image data
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_image_upload(test_image_data, google_vision_api_key)
    
    print(f"Success: {result['success']}")
    print(f"Entries: {len(result.get('portfolio', []))}")
    print(f"Errors: {len(result.get('errors', []))}")
    
    # Check for fake data
    has_fake_data = False
    if result.get('portfolio'):
        for entry in result['portfolio']:
            if (entry.get('amount_invested') in [2554.80, 2137.50] or 
                entry.get('buy_price') in [425.80, 142.50, 285.75]):
                has_fake_data = True
                break
    
    if not result['success'] and not has_fake_data and len(result.get('errors', [])) > 0:
        print("✅ Image processing correctly reports OCR failure!")
        print("✅ No fake data returned!")
        return True
    elif result['success'] and not has_fake_data:
        print("✅ Image processing works and returns real data!")
        return True
    else:
        print("❌ Image processing still has issues!")
        if has_fake_data:
            print("❌ Still returning fake data!")
        return False

def test_user_scenarios():
    """Test the exact scenarios the user reported"""
    print("\n🔍 Testing User's Exact Scenarios")
    print("=" * 40)
    
    print("User reported seeing:")
    print("  AMZN $2,554.80 $425.80")
    print("  NVDA $2,554.80 $425.80") 
    print("  COM  $2,554.80 $425.80")
    print()
    print("And also:")
    print("  TESLA $2,137.50 $285.75")
    print("  APPLE $2,137.50 $142.50")
    print()
    
    # Test multiple scenarios to ensure no fake data
    test_cases = [
        {'name': 'CSV Upload', 'type': 'spreadsheet'},
        {'name': 'Image Upload', 'type': 'image'}
    ]
    
    all_good = True
    
    for test_case in test_cases:
        print(f"Testing {test_case['name']}:")
        
        if test_case['type'] == 'spreadsheet':
            # Create realistic CSV
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(['A', 'B', 'C', 'D'])
            writer.writerow(['GOOGL', '1770.00', '161.00', '2024-01-01'])
            writer.writerow(['ASML', '1345.00', '668.50', '2024-01-02'])
            
            csv_content = output.getvalue().encode('utf-8')
            google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
            result = process_spreadsheet_upload(csv_content, 'test.csv', google_vision_api_key)
            
        else:  # image
            test_image_data = b'fake_portfolio_image_data'
            google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
            result = process_image_upload(test_image_data, google_vision_api_key)
        
        # Check for the specific fake data the user reported
        has_user_reported_fake_data = False
        if result.get('portfolio'):
            for entry in result['portfolio']:
                amount = entry.get('amount_invested', 0)
                price = entry.get('buy_price', 0)
                ticker = entry.get('ticker', '')
                
                # Check for exact fake data patterns user reported
                if ((amount == 2554.80 and price == 425.80) or
                    (amount == 2137.50 and price in [285.75, 142.50]) or
                    (ticker in ['COM'] and amount == 2554.80)):
                    has_user_reported_fake_data = True
                    print(f"  ❌ FOUND USER'S REPORTED FAKE DATA: {ticker} ${amount} @ ${price}")
                    break
        
        if has_user_reported_fake_data:
            print(f"  ❌ {test_case['name']} still returns fake data!")
            all_good = False
        else:
            print(f"  ✅ {test_case['name']} does not return fake data!")
    
    return all_good

def main():
    print("🚀 COMPLETE PORTFOLIO IMPORT FIX VERIFICATION")
    print("=" * 60)
    print("Testing both spreadsheet and image upload fixes")
    print("to ensure no more fake/default data is returned.")
    print()
    
    test1 = test_spreadsheet_fix()
    test2 = test_image_fix()
    test3 = test_user_scenarios()
    
    print("\n" + "=" * 60)
    print("📋 FINAL COMPREHENSIVE RESULTS:")
    print("=" * 60)
    
    if all([test1, test2, test3]):
        print("🎉 COMPLETE SUCCESS!")
        print()
        print("✅ Spreadsheet processing works correctly")
        print("✅ Image processing no longer returns fake data")
        print("✅ User's exact reported issues are resolved")
        print()
        print("🎯 THE USER'S ISSUES ARE COMPLETELY FIXED!")
        print()
        print("📝 What the user will now experience:")
        print("   • Spreadsheet uploads: Real data extracted correctly")
        print("   • Image uploads: Proper error message when OCR fails")
        print("   • No more fake data like AMZN $2,554.80 $425.80")
        print("   • No more fake data like TESLA $2,137.50 $285.75")
        print("   • Clear error messages with helpful suggestions")
        
    else:
        print("❌ Some issues remain!")
        if not test1:
            print("   - Spreadsheet processing issues")
        if not test2:
            print("   - Image processing issues")
        if not test3:
            print("   - User's specific scenarios not fully resolved")

if __name__ == "__main__":
    main()
