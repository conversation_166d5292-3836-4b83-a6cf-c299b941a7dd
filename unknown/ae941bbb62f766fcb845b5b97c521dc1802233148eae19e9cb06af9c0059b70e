#!/usr/bin/env python3
"""
Currency Bug Detector - Test script to verify currency preservation in portfolio import
"""

import requests
import json

def test_currency_detection():
    """Test the currency detection with Danish portfolio text"""
    
    # Test data with Danish currency symbols
    test_text = """
    Portfolio Overview:
    
    Novo Nordisk: 1.195,00 kr invested, buy price: 850,00 kr
    Vestas: 2.500,00 kr invested, buy price: 125,50 kr  
    <PERSON>sberg: 1.800,00 kr invested, buy price: 900,00 kr
    """
    
    # Test with different user currency settings
    test_cases = [
        {"user_currency": "USD", "description": "User prefers USD but source is DKK"},
        {"user_currency": "DKK", "description": "User prefers DKK and source is DKK"},
        {"user_currency": "EUR", "description": "User prefers EUR but source is DKK"}
    ]
    
    print("🔍 Testing Currency Detection Bug")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['description']}")
        print("-" * 40)
        
        # Make request to test endpoint
        try:
            response = requests.post('http://localhost:9877/api/import/test', 
                json={
                    'text': test_text,
                    'user_currency': test_case['user_currency']
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print(f"✅ Request successful")
                    print(f"🎯 User Portfolio Currency: {result.get('user_portfolio_currency')}")
                    print(f"🌍 Detected Currency: {result.get('detected_currency')}")
                    
                    # Analyze the AI result
                    ai_result = result.get('ai_result', {})
                    portfolio_data = ai_result.get('portfolio_data', [])
                    
                    print(f"📊 Portfolio Entries Found: {len(portfolio_data)}")
                    
                    for j, entry in enumerate(portfolio_data, 1):
                        ticker = entry.get('ticker', 'Unknown')
                        amount_invested = entry.get('amount_invested', 'N/A')
                        amount_invested_currency = entry.get('amount_invested_currency', 'N/A')
                        buy_price = entry.get('buy_price', 'N/A')
                        buy_price_currency = entry.get('buy_price_currency', 'N/A')
                        
                        print(f"   {j}. {ticker}:")
                        print(f"      💰 Amount Invested: {amount_invested} {amount_invested_currency}")
                        print(f"      💵 Buy Price: {buy_price} {buy_price_currency}")
                        
                        # Check for the bug
                        if amount_invested_currency == 'USD' and 'kr' in test_text:
                            print(f"      🚨 BUG DETECTED: Currency shows USD but source has 'kr'!")
                        elif amount_invested_currency in ['DKK', 'kr']:
                            print(f"      ✅ Currency correctly preserved from source")
                        else:
                            print(f"      ⚠️  Unexpected currency: {amount_invested_currency}")
                    
                else:
                    print(f"❌ Request failed: {result.get('error')}")
                    
            else:
                print(f"❌ HTTP Error {response.status_code}: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection failed - make sure the Flask app is running on localhost:9877")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print("- If currencies show 'USD' when source has 'kr', the bug exists")
    print("- If currencies show 'DKK' or preserve 'kr', the fix is working")
    print("- The system should preserve original detected currency, not convert to user preference")

if __name__ == "__main__":
    test_currency_detection()
