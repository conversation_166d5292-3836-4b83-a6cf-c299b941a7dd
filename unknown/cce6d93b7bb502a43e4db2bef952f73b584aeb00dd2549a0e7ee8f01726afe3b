#!/usr/bin/env python3
"""
Test the number parsing fix
"""

from portfolio_import import AIPortfolioExtractor

def test_number_parsing():
    """Test the fixed number parsing logic"""
    
    extractor = AIPortfolioExtractor("dummy_key", "USD")
    
    test_values = [
        "2074.53",      # Should be 2074.53
        "173.38",       # Should be 173.38
        "4841.20",      # Should be 4841.20
        "750.09",       # Should be 750.09
        "2074,53",      # European format, should be 2074.53
        "1,234.56",     # US format, should be 1234.56
        "1.234,56",     # European format, should be 1234.56
        "15.848",       # Could be 15848 or 15.848
        "2.462",        # Could be 2462 or 2.462
    ]
    
    print("🧪 Testing number parsing fix...")
    print("=" * 50)
    
    for value in test_values:
        try:
            parsed = extractor._parse_financial_value(value)
            print(f"'{value}' -> {parsed}")
        except Exception as e:
            print(f"'{value}' -> ERROR: {e}")
    
    print("\n✅ Number parsing test completed!")

if __name__ == "__main__":
    test_number_parsing()
