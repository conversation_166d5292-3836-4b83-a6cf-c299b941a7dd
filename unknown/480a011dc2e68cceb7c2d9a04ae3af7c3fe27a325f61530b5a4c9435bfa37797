#!/usr/bin/env python3
"""
Debug the complete currency detection logic to find why USD is winning
"""

import re

def test_full_currency_logic():
    """Test the complete currency detection logic"""
    
    test_text = """
Company | Value (¥) | Change % | Today | Price (USD)
Shopify Inc. | ¥1,803,220 | ▲ 13.71 % | ↑ 0.12 % | USD 85.77
Palantir Tech. | ¥704300 | ▼ 1.88% | → 0.00 % | $25.91
Roblox Corp. | ¥1 020 050 | ▲ 9.02% | ↑ 0.31% | 39.67 USD
Pinterest Inc. | ¥892,430 | ▲0.96 % | ↑ 0.06% | 43.11
Block Inc. | ¥2.370.100 | ▼ 4.20% | ↑ 0.21% | USD: 70.30
"""

    print("🔍 DEBUGGING COMPLETE CURRENCY DETECTION LOGIC")
    print("=" * 60)
    print(f"Test text:\n{test_text}")
    print("=" * 60)
    
    text_lower = test_text.lower()
    currency_counts = {}
    
    # Currency symbols from the actual code
    currency_symbols = {
        '$': 'USD',
        '€': 'EUR',
        '£': 'GBP',
        '¥': 'JPY',  # Japanese Yen
        'Y': 'JPY',  # Alternative Yen representation
        'kr': 'DKK',
        'Kc': 'CZK',
        'Kč': 'CZK',
    }
    
    print("\n1. SYMBOL-BASED DETECTION WITH PATTERN MATCHING")
    print("-" * 50)
    
    for symbol, currency in currency_symbols.items():
        symbol_count = test_text.count(symbol)
        if symbol_count > 0:
            print(f"\nProcessing symbol '{symbol}' -> {currency}")
            print(f"  Raw symbol count: {symbol_count}")
            
            # Pattern matching logic from the actual code
            currency_patterns = []
            escaped_symbol = re.escape(symbol)
            
            # Standard patterns
            currency_patterns.extend([
                f'{escaped_symbol}[\\d,]+(?:\\.\\d+)?',  # Symbol followed by numbers: $1,000.50
                f'{escaped_symbol}\\s*[\\d,]+(?:\\.\\d+)?',  # Symbol with optional space: $ 1,000.50
                f'[\\d,]+(?:\\.\\d+)?\\s*{escaped_symbol}',  # Numbers followed by symbol: 1,000.50 €
                f'[\\d,]+(?:\\.\\d+)?\\s+{escaped_symbol}',  # Numbers with space then symbol: 1,000.50 USD
            ])
            
            # Special handling for multi-character symbols
            if len(symbol) > 1:
                currency_patterns.extend([
                    f'{escaped_symbol}:\\s*[\\d,]+(?:\\.\\d+)?',  # USD: 85.77
                    f'{escaped_symbol}\\s+[\\d,]+(?:\\.\\d+)?',   # USD 85.77
                ])
            
            # Count pattern matches
            pattern_matches = 0
            for pattern in currency_patterns:
                matches = re.findall(pattern, test_text, re.IGNORECASE)
                if matches:
                    print(f"    Pattern '{pattern}': {len(matches)} matches - {matches}")
                pattern_matches += len(matches)
            
            print(f"  Total pattern matches: {pattern_matches}")
            
            # Calculate weight multiplier
            if pattern_matches > 0:
                weight_multiplier = min(10, 5 + pattern_matches)  # Scale with matches, max 10
                print(f"  Weight multiplier: min(10, 5 + {pattern_matches}) = {weight_multiplier}")
            else:
                weight_multiplier = 3  # Base confidence for symbol alone
                print(f"  Weight multiplier: {weight_multiplier} (base)")
            
            # Calculate final score
            final_score = symbol_count * weight_multiplier
            print(f"  Final score: {symbol_count} × {weight_multiplier} = {final_score}")
            
            currency_counts[currency] = currency_counts.get(currency, 0) + final_score
    
    print(f"\nCurrency counts after symbol processing: {currency_counts}")
    
    # Currency code detection (USD, EUR, etc.)
    print("\n2. CURRENCY CODE DETECTION")
    print("-" * 30)
    
    currency_codes = ['USD', 'EUR', 'GBP', 'JPY', 'DKK', 'SEK', 'NOK', 'CHF']
    for code in currency_codes:
        code_count = test_text.upper().count(code)
        if code_count > 0:
            print(f"Currency code '{code}': {code_count} occurrences")
            currency_counts[code] = currency_counts.get(code, 0) + code_count
    
    print(f"Currency counts after code detection: {currency_counts}")
    
    # Mixed currency indicators
    print("\n3. MIXED CURRENCY INDICATORS")
    print("-" * 35)
    
    mixed_currency_indicators = [
        ('GAK', 'USD'),
        ('markedsværdi', 'DKK'),
        ('afkast', 'DKK'),
        ('Price (USD)', 'USD'),
        ('Price (EUR)', 'EUR'),
        ('Price (GBP)', 'GBP'),
        ('USD:', 'USD'),
        ('EUR:', 'EUR'),
        ('GBP:', 'GBP'),
    ]
    
    for indicator, likely_currency in mixed_currency_indicators:
        if indicator.lower() in text_lower:
            print(f"Found indicator '{indicator}' -> boosting {likely_currency} by +2")
            currency_counts[likely_currency] = currency_counts.get(likely_currency, 0) + 2
    
    print(f"Currency counts after mixed indicators: {currency_counts}")
    
    # Final results
    print("\n4. FINAL RESULTS")
    print("-" * 20)
    
    if currency_counts:
        sorted_currencies = sorted(currency_counts.items(), key=lambda x: x[1], reverse=True)
        print(f"Final ranking:")
        for i, (currency, count) in enumerate(sorted_currencies, 1):
            print(f"  {i}. {currency}: {count} points")
        
        winner = sorted_currencies[0][0]
        if winner == 'JPY':
            print("\n✅ JPY correctly wins!")
        else:
            print(f"\n❌ {winner} wins, but JPY should win!")
    else:
        print("No currencies detected!")

if __name__ == "__main__":
    test_full_currency_logic()
