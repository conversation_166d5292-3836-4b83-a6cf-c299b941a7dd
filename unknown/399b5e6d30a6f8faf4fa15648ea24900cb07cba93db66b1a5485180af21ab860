#!/usr/bin/env python3
"""
Test the Danish portfolio format fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService, PortfolioEntry

def test_danish_portfolio_extraction():
    """Test the specific Danish portfolio format from your image."""
    print("Testing Danish portfolio extraction fix...")
    
    service = PortfolioImportService("test_api_key")
    
    # Exact text that should be extracted from your image
    danish_text = """
    GOOGL
    
    Mine beholdninger
    
    Antal
    13 stk
    
    GAK
    161,61 USD
    
    Markedsværdi
    2.462,85 USD
    
    Ureal.afkast
    +9,9%
    """
    
    print("Testing Danish portfolio parsing...")
    result = service.extract_portfolio_from_text(danish_text)
    
    print(f"Success: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    if result.portfolio_entries:
        entry = result.portfolio_entries[0]
        print(f"\nExtracted data:")
        print(f"  Ticker: {entry.ticker}")
        print(f"  Shares: {entry.shares}")
        print(f"  Buy Price: ${entry.buy_price:.2f}")
        print(f"  Amount Invested: ${entry.amount_invested:.2f}")
        print(f"  Current Value: ${entry.current_value:.2f}")
        
        # Verify the calculation
        expected_invested = 13 * 161.61  # 13 shares * $161.61 = $2100.93
        print(f"\nVerification:")
        print(f"  Expected invested amount: ${expected_invested:.2f}")
        print(f"  Calculated invested amount: ${entry.amount_invested:.2f}")
        print(f"  Calculation correct: {abs(entry.amount_invested - expected_invested) < 0.01}")
        
        return entry.amount_invested > 0
    else:
        print("No entries extracted!")
        return False

def test_portfolio_entry_calculation():
    """Test the PortfolioEntry calculation directly."""
    print("\nTesting PortfolioEntry calculation...")
    
    # Test the exact scenario from your image
    entry = PortfolioEntry(
        ticker="GOOGL",
        shares=13.0,
        buy_price=161.61,
        current_value=2462.85,
        amount_invested=0.0  # This should be calculated
    )
    
    print(f"Input:")
    print(f"  Ticker: {entry.ticker}")
    print(f"  Shares: {entry.shares}")
    print(f"  Buy Price: ${entry.buy_price}")
    print(f"  Current Value: ${entry.current_value}")
    
    print(f"\nAfter calculation:")
    print(f"  Amount Invested: ${entry.amount_invested:.2f}")
    
    expected_invested = 13 * 161.61  # $2100.93
    calculation_correct = abs(entry.amount_invested - expected_invested) < 0.01
    
    print(f"\nVerification:")
    print(f"  Expected: ${expected_invested:.2f}")
    print(f"  Calculated: ${entry.amount_invested:.2f}")
    print(f"  Correct: {calculation_correct}")
    
    return calculation_correct

def test_danish_format_parser():
    """Test the Danish format parser specifically."""
    print("\nTesting Danish format parser...")
    
    service = PortfolioImportService("test_api_key")
    
    # Test text with Danish format
    test_text = """
    GOOGL
    Mine beholdninger
    Antal: 13 stk
    GAK: 161,61 USD
    Markedsværdi: 2.462,85 USD
    """
    
    # Test the Danish parser directly
    result = service.ai_extractor._parse_danish_portfolio_format(test_text)
    
    if result:
        print(f"Danish parser result:")
        print(f"  Ticker: {result['ticker']}")
        print(f"  Shares: {result['shares']}")
        print(f"  Buy Price: ${result['buy_price']}")
        print(f"  Current Value: ${result['current_value']}")
        
        # Create PortfolioEntry to test calculation
        entry = PortfolioEntry(**result)
        print(f"  Amount Invested (calculated): ${entry.amount_invested:.2f}")
        
        return entry.amount_invested > 0
    else:
        print("Danish parser returned None")
        return False

def test_number_parsing():
    """Test Danish number format parsing."""
    print("\nTesting Danish number format parsing...")
    
    test_cases = [
        ("2.462,85", 2462.85),
        ("161,61", 161.61),
        ("13", 13.0),
        ("1.234,56", 1234.56),
    ]
    
    for input_str, expected in test_cases:
        # Simulate the parsing logic
        if '.' in input_str and ',' in input_str:
            # Danish format: 2.462,85 -> 2462.85
            parsed = float(input_str.replace('.', '').replace(',', '.'))
        elif ',' in input_str:
            # Just replace decimal separator
            parsed = float(input_str.replace(',', '.'))
        else:
            parsed = float(input_str)
        
        print(f"  '{input_str}' -> {parsed} (expected: {expected})")
        
        if abs(parsed - expected) > 0.01:
            print(f"    ERROR: Parsing failed!")
            return False
    
    print("  All number parsing tests passed!")
    return True

if __name__ == "__main__":
    print("Danish Portfolio Fix Test")
    print("=" * 50)
    
    # Test 1: Number parsing
    number_test = test_number_parsing()
    print(f"Number parsing test: {'PASS' if number_test else 'FAIL'}")
    
    # Test 2: PortfolioEntry calculation
    entry_test = test_portfolio_entry_calculation()
    print(f"PortfolioEntry calculation test: {'PASS' if entry_test else 'FAIL'}")
    
    # Test 3: Danish format parser
    parser_test = test_danish_format_parser()
    print(f"Danish format parser test: {'PASS' if parser_test else 'FAIL'}")
    
    # Test 4: Full extraction
    extraction_test = test_danish_portfolio_extraction()
    print(f"Danish portfolio extraction test: {'PASS' if extraction_test else 'FAIL'}")
    
    print("\n" + "=" * 50)
    
    overall_success = all([number_test, entry_test, parser_test, extraction_test])
    print(f"Overall test result: {'PASS' if overall_success else 'FAIL'}")
    
    if overall_success:
        print("\n✅ Danish portfolio fix is working!")
        print("The system should now correctly:")
        print("• Extract GOOGL ticker from header")
        print("• Parse '13 stk' as 13 shares")
        print("• Parse 'GAK: 161,61 USD' as $161.61 buy price")
        print("• Parse 'Markedsværdi: 2.462,85 USD' as $2462.85 current value")
        print("• Calculate amount invested as 13 × $161.61 = $2100.93")
    else:
        print("\n❌ Some tests failed. The fix needs more work.")
