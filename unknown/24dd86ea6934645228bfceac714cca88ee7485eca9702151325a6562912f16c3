#!/usr/bin/env python3

print("Starting test...")

try:
    from portfolio_import import AIPortfolioExtractor
    print("✅ Import successful")
    
    # Initialize the AI extractor
    extractor = AIPortfolioExtractor("test_key", "test_eodhd_key")
    print("✅ Extractor initialized")
    
    # Set user's portfolio currency to USD
    extractor.user_portfolio_currency = "USD"
    print(f"✅ User portfolio currency set to: {extractor.user_portfolio_currency}")
    
    # Test simple calculation
    test_data = [{
        'ticker': 'GOOGL',
        'amount_invested': 10000,
        'amount_invested_currency': 'USD',
        'buy_price': 120.50,
        'buy_price_currency': 'USD',
        'shares': 0
    }]
    
    print("✅ Test data created")
    print("Processing...")
    
    result = extractor._process_gemini_response(test_data)
    print(f"✅ Processing complete. Results: {len(result)} entries")
    
    if result:
        entry = result[0]
        expected_shares = 10000 / 120.50
        actual_shares = entry['shares']
        print(f"Expected shares: {expected_shares:.6f}")
        print(f"Actual shares: {actual_shares:.6f}")
        print(f"Difference: {abs(actual_shares - expected_shares):.6f}")
        
        if abs(actual_shares - expected_shares) < 0.001:
            print("✅ Share calculation is CORRECT!")
        else:
            print("❌ Share calculation is INCORRECT!")
    else:
        print("❌ No results returned")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("Test complete.")
