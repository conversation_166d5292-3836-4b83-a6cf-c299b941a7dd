#!/usr/bin/env python3
"""
Test the improved portfolio extraction system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService

def test_danish_portfolio():
    """Test with your Danish portfolio example."""
    print("Testing Danish portfolio extraction...")
    
    service = PortfolioImportService("test_api_key")
    
    # Your actual Danish portfolio text
    danish_text = """
    Mine beholdninger
    
    Antal: 13 stk
    GAK: 161,61 USD
    
    Markedsværdi: 2.462,85 USD
    Ureal.afkast: +9,9%
    
    Vigtige datoer
    Dage til regnskab: 1
    Næste regnskab: 23. jul.
    
    Om virksomheden
    Alphabet is a holding company that wholly owns
    internet giant Google. The California-based
    company derives slightly less ... Læs mere
    
    NasdaqGS:GOOGL 10 161 DKK 12,216.61 18.1%
    """
    
    print("Extracting from Danish text...")
    result = service.extract_portfolio_from_text(danish_text)
    
    print(f"Success: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    for entry in result.portfolio_entries:
        print(f"  Ticker: {entry.ticker}")
        print(f"  Shares: {entry.shares}")
        print(f"  Buy Price: ${entry.buy_price:.2f}")
        print(f"  Amount Invested: ${entry.amount_invested:.2f}")
        print(f"  Current Value: ${entry.current_value:.2f}")
        print()
    
    return result.success and len(result.portfolio_entries) > 0

def test_ticker_extraction():
    """Test ticker extraction specifically."""
    print("Testing ticker extraction...")
    
    service = PortfolioImportService("test_api_key")
    
    test_cases = [
        ("NasdaqGS:GOOGL 10 shares", ["GOOGL"]),
        ("AAPL 100 shares at $150", ["AAPL"]),
        ("Mine beholdninger GAK værdi", []),  # Should not extract GAK, MINE
        ("Tesla Inc. (TSLA) 5 shares", ["TSLA"]),
        ("MSFT AMZN META", ["MSFT", "AMZN", "META"]),
    ]
    
    for text, expected in test_cases:
        tickers = service.ai_extractor._extract_tickers_intelligently(text)
        print(f"Text: '{text}'")
        print(f"Expected: {expected}")
        print(f"Found: {list(tickers)}")
        print(f"Match: {set(tickers) == set(expected)}")
        print()

def test_ocr_simulation():
    """Test with OCR-like extracted text."""
    print("Testing OCR simulation...")
    
    service = PortfolioImportService("test_api_key")
    
    # Simulate OCR output from a portfolio screenshot
    ocr_text = """
    GOOGL: 10 shares
    Price: $161.61
    Value: $1,616.10
    
    AAPL: 25 shares  
    Price: $150.00
    Value: $3,750.00
    
    Total Portfolio: $5,366.10
    """
    
    result = service.extract_portfolio_from_text(ocr_text)
    
    print(f"OCR extraction successful: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    
    for entry in result.portfolio_entries:
        print(f"  {entry.ticker}: {entry.shares} shares @ ${entry.buy_price}")
    
    return result.success

def test_current_value_calculation():
    """Test current value to invested amount calculation."""
    print("Testing current value calculation...")
    
    from portfolio_import import PortfolioEntry
    
    # Test scenario: only have current value and buy price
    entry = PortfolioEntry(
        ticker="AAPL",
        current_value=5000.0,  # $5000 current value
        buy_price=150.0,       # Bought at $150
        current_price=200.0    # Current price $200
    )
    
    print(f"Input: Current Value=${entry.current_value}, Buy Price=${entry.buy_price}, Current Price=${entry.current_price}")
    print(f"Calculated: Shares={entry.shares}, Amount Invested=${entry.amount_invested}")
    
    # Verify calculation
    expected_shares = 5000.0 / 200.0  # 25 shares
    expected_invested = 25 * 150.0    # $3750
    
    success = (abs(entry.shares - expected_shares) < 0.01 and 
               abs(entry.amount_invested - expected_invested) < 0.01)
    
    print(f"Calculation correct: {success}")
    return success

if __name__ == "__main__":
    print("Improved Portfolio Extraction Test")
    print("=" * 50)
    
    # Test 1: Danish portfolio
    danish_test = test_danish_portfolio()
    print(f"Danish portfolio test: {'PASS' if danish_test else 'FAIL'}")
    print()
    
    # Test 2: Ticker extraction
    print("=" * 50)
    test_ticker_extraction()
    
    # Test 3: OCR simulation
    print("=" * 50)
    ocr_test = test_ocr_simulation()
    print(f"OCR simulation test: {'PASS' if ocr_test else 'FAIL'}")
    print()
    
    # Test 4: Current value calculation
    print("=" * 50)
    calc_test = test_current_value_calculation()
    print(f"Current value calculation test: {'PASS' if calc_test else 'FAIL'}")
    
    print("\n" + "=" * 50)
    
    overall_success = all([danish_test, ocr_test, calc_test])
    print(f"Overall test result: {'PASS' if overall_success else 'FAIL'}")
    
    if overall_success:
        print("\n✅ The enhanced portfolio import system is working correctly!")
        print("It should now handle:")
        print("• Multilingual portfolio data (Danish, German, French, etc.)")
        print("• Any currency with automatic conversion")
        print("• Current value to invested amount calculation")
        print("• Better ticker extraction (fewer false positives)")
        print("• Robust OCR with multiple fallback methods")
    else:
        print("\n❌ Some tests failed. The system may need further adjustments.")
