#!/usr/bin/env python3
"""
Simple test to check what Gemini AI is actually returning
"""

import os
import json

# Set the Google API key
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'

def test_gemini_directly():
    """Test Gemini AI directly with Danish portfolio text"""
    
    try:
        import google.generativeai as genai
        
        # Configure Gemini
        genai.configure(api_key=os.environ['GOOGLE_API_KEY'])
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Test text with Danish currency
        test_text = """
        Portfolio Overview - Danske Bank
        
        GOOGL - Google Inc.
        Antal aktier: 11,96
        GAK (køb): 1.195,00 kr
        Markedsværdi: 14.300,00 kr
        
        AMZN - Amazon
        Antal aktier: 11,98  
        GAK (køb): 1.344,00 kr
        Markedsværdi: 16.100,00 kr
        """
        
        # Simple prompt that should detect DKK
        prompt = """
        Extract portfolio data from this text and return as JSON.
        
        For each stock, return:
        {
          "ticker": "GOOGL",
          "shares": 11.96,
          "buy_price": 1195.00,
          "buy_price_currency": "DKK",
          "amount_invested": 14300.00,
          "amount_invested_currency": "DKK",
          "current_value": 14300.00,
          "current_value_currency": "DKK"
        }
        
        CRITICAL: Detect the currency from the text. "kr" means DKK (Danish Krone).
        
        Text:
        """ + test_text
        
        print("🧠 Testing Gemini AI directly...")
        print(f"📝 Input text: {test_text}")
        print(f"🎯 Expected currency: DKK (because of 'kr' symbols)")
        print()
        
        # Call Gemini
        response = model.generate_content(prompt)
        response_text = response.text
        
        print("🤖 Gemini AI Raw Response:")
        print("-" * 50)
        print(response_text)
        print("-" * 50)
        
        # Try to parse as JSON
        try:
            # Extract JSON from response
            json_start = response_text.find('[')
            json_end = response_text.rfind(']') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                portfolio_data = json.loads(json_text)
                
                print("\n✅ Successfully parsed JSON response:")
                print(json.dumps(portfolio_data, indent=2))
                
                # Check currencies
                print("\n🔍 Currency Analysis:")
                for i, entry in enumerate(portfolio_data):
                    ticker = entry.get('ticker', 'N/A')
                    amount_currency = entry.get('amount_invested_currency', 'NOT_SET')
                    buy_currency = entry.get('buy_price_currency', 'NOT_SET')
                    
                    print(f"Entry {i+1} ({ticker}):")
                    print(f"  💰 amount_invested_currency: {amount_currency}")
                    print(f"  💵 buy_price_currency: {buy_currency}")
                    
                    if amount_currency == 'DKK':
                        print(f"  ✅ CORRECT: Gemini detected DKK for amount_invested")
                    elif amount_currency == 'USD':
                        print(f"  ❌ BUG: Gemini returned USD instead of DKK")
                    else:
                        print(f"  ⚠️  UNEXPECTED: Gemini returned {amount_currency}")
                        
            else:
                print("❌ Could not find JSON in response")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            
    except Exception as e:
        print(f"❌ Error testing Gemini: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gemini_directly()
