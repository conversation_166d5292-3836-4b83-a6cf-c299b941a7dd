#!/usr/bin/env python3
"""
Test the exact user image scenario with simulated OCR text
"""

from portfolio_import import PortfolioImportService

def test_user_exact_image():
    """Test with simulated OCR text from the user's image"""
    print("🔍 Testing User's Exact Image OCR Text")
    print("=" * 50)
    
    # Simulate the OCR text that would be extracted from the user's image
    # Based on the image showing:
    # Ticker | Shares | Avg. Cost Basis | Market Value (DKK) | % Chg.
    # Alphabet Inc. NasdaqGS:GOOGL | 10 | 161 | DKK 12,216.61 | 18.1%
    # ASML Holding N.V. NasdaqGS:ASML | 2 | 668.5 | DKK 9,279.65 | 8.1%
    # Uber Technologies, Inc. NYSE:UBER | 10 | 74.59 | DKK 5,851.40 | 22.1%
    # Amazon.com, Inc. NasdaqGS:AMZN | 8 | 186.92 | DKK 11,790.22 | 22.7%
    
    simulated_ocr_text = """
Ticker Shares Avg. Cost Basis Market Value (DKK) % Chg.

Alphabet Inc.
NasdaqGS:GOOGL 10 161 DKK 12,216.61 18.1%

ASML Holding N.V.
NasdaqGS:ASML 2 668.5 DKK 9,279.65 8.1%

Uber Technologies, Inc.
NYSE:UBER 10 74.59 DKK 5,851.40 22.1%

Amazon.com, Inc.
NasdaqGS:AMZN 8 186.92 DKK 11,790.22 22.7%
"""
    
    print("Simulated OCR text:")
    print(simulated_ocr_text)
    print("\n" + "=" * 50)
    
    # Test the extraction
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    print("Processing with improved extraction logic...")
    result = service.extract_portfolio_from_text(simulated_ocr_text)
    
    print(f"\nResult:")
    print(f"Success: {result.success}")
    print(f"Portfolio entries: {len(result.portfolio_entries)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Warnings: {len(result.warnings)}")
    
    if result.errors:
        print(f"\nErrors:")
        for error in result.errors:
            print(f"  - {error}")
    
    if result.warnings:
        print(f"\nWarnings:")
        for warning in result.warnings:
            print(f"  - {warning}")
    
    if result.portfolio_entries:
        print(f"\nExtracted Portfolio:")
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: {entry.shares} shares @ {entry.buy_price} = {entry.amount_invested}")
    
    # Check if we got the correct data
    expected_tickers = ['GOOGL', 'ASML', 'UBER', 'AMZN']
    extracted_tickers = [entry.ticker for entry in result.portfolio_entries]
    
    print(f"\n📋 Evaluation:")
    print(f"Expected tickers: {expected_tickers}")
    print(f"Extracted tickers: {extracted_tickers}")
    
    success = True
    for ticker in expected_tickers:
        if ticker in extracted_tickers:
            print(f"✅ {ticker} - Found")
        else:
            print(f"❌ {ticker} - Missing")
            success = False
    
    # Check for DKK currency confusion
    if 'DKK' in extracted_tickers:
        print(f"❌ DKK incorrectly identified as ticker")
        success = False
    else:
        print(f"✅ DKK correctly filtered out")
    
    return success, result

def test_alternative_ocr_formats():
    """Test different OCR text formats that might be extracted"""
    print("\n🔍 Testing Alternative OCR Formats")
    print("=" * 50)
    
    test_cases = [
        {
            'name': 'Compact format',
            'text': """
Alphabet Inc. NasdaqGS:GOOGL 10 161 DKK 12,216.61
ASML Holding N.V. NasdaqGS:ASML 2 668.5 DKK 9,279.65
Uber Technologies, Inc. NYSE:UBER 10 74.59 DKK 5,851.40
Amazon.com, Inc. NasdaqGS:AMZN 8 186.92 DKK 11,790.22
"""
        },
        {
            'name': 'Separated lines',
            'text': """
Alphabet Inc.
NasdaqGS:GOOGL
10
161
DKK 12,216.61

ASML Holding N.V.
NasdaqGS:ASML
2
668.5
DKK 9,279.65
"""
        },
        {
            'name': 'Mixed format',
            'text': """
GOOGL 10 shares at 161 each, total DKK 12,216.61
ASML: 2 @ 668.5 = DKK 9,279.65
UBER 10 74.59 DKK 5,851.40
AMZN 8 186.92 DKK 11,790.22
"""
        }
    ]
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    all_success = True
    
    for test_case in test_cases:
        print(f"\nTesting {test_case['name']}:")
        result = service.extract_portfolio_from_text(test_case['text'])
        
        extracted_tickers = [entry.ticker for entry in result.portfolio_entries]
        print(f"  Extracted: {extracted_tickers}")
        
        # Check if we got at least some of the expected tickers
        expected_tickers = ['GOOGL', 'ASML', 'UBER', 'AMZN']
        found_count = sum(1 for ticker in expected_tickers if ticker in extracted_tickers)
        
        if found_count >= 2:  # At least half
            print(f"  ✅ Found {found_count}/4 tickers")
        else:
            print(f"  ❌ Only found {found_count}/4 tickers")
            all_success = False
        
        # Check for DKK confusion
        if 'DKK' in extracted_tickers:
            print(f"  ❌ DKK incorrectly identified as ticker")
            all_success = False
    
    return all_success

def main():
    print("🚀 TESTING USER'S EXACT IMAGE SCENARIO")
    print("=" * 60)
    print("Testing the improved portfolio import with simulated")
    print("OCR text from the user's portfolio screenshot")
    print()
    
    test1_success, result = test_user_exact_image()
    test2_success = test_alternative_ocr_formats()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    
    if test1_success and test2_success:
        print("🎉 SUCCESS! Improved extraction works correctly!")
        print()
        print("✅ Correctly extracts GOOGL, ASML, UBER, AMZN")
        print("✅ Filters out DKK currency code")
        print("✅ Handles exchange-prefixed tickers (NasdaqGS:GOOGL)")
        print("✅ Works with various OCR text formats")
        print()
        print("🎯 The user's image import issue should be RESOLVED!")
        
    else:
        print("❌ Some issues remain!")
        print("   The extraction logic needs further improvement.")
        
        if not test1_success:
            print("   - Main extraction test failed")
        if not test2_success:
            print("   - Alternative format tests failed")

if __name__ == "__main__":
    main()
