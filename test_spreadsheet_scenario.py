#!/usr/bin/env python3
"""
Test the exact spreadsheet scenario that was failing for the user
"""

import io
import csv
from portfolio_import import process_spreadsheet_upload

def test_user_spreadsheet_scenario():
    """Test the exact scenario the user reported with spreadsheet upload"""
    print("Testing User's Spreadsheet Upload Scenario")
    print("=" * 50)
    
    # Create CSV that matches what user might upload
    output = io.StringIO()
    writer = csv.writer(output)
    
    # User's data format - this should NOT return default data
    writer.writerow(['Ticker', 'Amount Invested', 'Buy Price', 'Purchase Date'])
    writer.writerow(['AAPL', '5000.00', '175.25', '2024-01-15'])
    writer.writerow(['MSFT', '3000.00', '380.50', '2024-02-10'])
    writer.writerow(['NVDA', '2000.00', '850.75', '2024-03-05'])
    
    csv_content = output.getvalue().encode('utf-8')
    
    # Process using the web interface function
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_content, 'portfolio.csv', google_vision_api_key)
    
    print(f"Result:")
    print(f"Success: {result['success']}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Warnings: {result.get('warnings', [])}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    
    # Check the actual data
    portfolio = result.get('portfolio', [])
    if portfolio:
        print(f"\nActual portfolio data:")
        for entry in portfolio:
            print(f"  {entry['ticker']}: ${entry['amount_invested']} @ ${entry['buy_price']} on {entry['purchase_date']}")
        
        # Verify this is NOT the default data the user was seeing
        # User reported seeing: AMZN $2,554.80 $425.80, NVDA $2,554.80 $425.80, COM $2,554.80 $425.80
        default_amounts = [2554.80]  # From user's error message
        default_prices = [425.80]   # From user's error message
        
        actual_tickers = [entry['ticker'] for entry in portfolio]
        actual_amounts = [entry['amount_invested'] for entry in portfolio]
        actual_prices = [entry['buy_price'] for entry in portfolio]
        
        # Check if we're getting the user's actual data
        has_user_data = (
            'AAPL' in actual_tickers and 
            5000.0 in actual_amounts and 
            175.25 in actual_prices
        )
        
        # Check if we're getting default data (which would be bad)
        has_default_data = (
            any(amount in default_amounts for amount in actual_amounts) or
            any(price in default_prices for price in actual_prices)
        )
        
        print(f"\nData validation:")
        print(f"  Has user's actual data: {has_user_data}")
        print(f"  Has default/mock data: {has_default_data}")
        
        if has_user_data and not has_default_data:
            print("\n✅ SUCCESS: Processing user's actual data correctly!")
            print("✅ No default/mock data detected!")
            return True
        elif has_default_data:
            print("\n❌ PROBLEM: Still returning default/mock data!")
            print("❌ This is the exact issue the user reported!")
            return False
        else:
            print("\n❌ PROBLEM: Not processing user's data correctly!")
            return False
    else:
        print("\n❌ PROBLEM: No portfolio entries found!")
        return False

def test_simple_columns_scenario():
    """Test simple A,B,C,D column scenario"""
    print("\n\nTesting Simple Columns Scenario (A,B,C,D)")
    print("=" * 50)
    
    # Create CSV with simple column names - this was also failing
    output = io.StringIO()
    writer = csv.writer(output)
    
    writer.writerow(['A', 'B', 'C', 'D'])
    writer.writerow(['TSLA', '4000.00', '200.00', '2024-01-20'])
    writer.writerow(['GOOGL', '6000.00', '150.00', '2024-02-15'])
    
    csv_content = output.getvalue().encode('utf-8')
    
    # Process using the web interface function
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_content, 'simple.csv', google_vision_api_key)
    
    print(f"Result:")
    print(f"Success: {result['success']}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    
    portfolio = result.get('portfolio', [])
    if portfolio:
        print(f"\nProcessed data:")
        for entry in portfolio:
            print(f"  {entry['ticker']}: ${entry['amount_invested']} @ ${entry['buy_price']}")
        
        # Check for correct data
        has_tsla = any(entry['ticker'] == 'TSLA' and entry['amount_invested'] == 4000.0 for entry in portfolio)
        has_googl = any(entry['ticker'] == 'GOOGL' and entry['amount_invested'] == 6000.0 for entry in portfolio)
        
        # Check for default data
        has_default_data = any(entry['amount_invested'] == 2554.80 or entry['buy_price'] == 425.80 for entry in portfolio)
        
        print(f"\nValidation:")
        print(f"  Has TSLA data: {has_tsla}")
        print(f"  Has GOOGL data: {has_googl}")
        print(f"  Has default data: {has_default_data}")
        
        if has_tsla and has_googl and not has_default_data:
            print("\n✅ SUCCESS: Simple columns processed correctly!")
            return True
        else:
            print("\n❌ PROBLEM: Simple columns not processed correctly!")
            return False
    else:
        print("\n❌ PROBLEM: No entries found for simple columns!")
        return False

def test_error_handling():
    """Test that errors don't fall back to default data"""
    print("\n\nTesting Error Handling (No Default Data Fallback)")
    print("=" * 50)
    
    # Create invalid CSV that should fail
    output = io.StringIO()
    writer = csv.writer(output)
    
    writer.writerow(['Invalid', 'Column', 'Names'])
    writer.writerow(['Not', 'A', 'Ticker'])
    writer.writerow(['123', '456', '789'])
    
    csv_content = output.getvalue().encode('utf-8')
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_content, 'invalid.csv', google_vision_api_key)
    
    print(f"Result:")
    print(f"Success: {result['success']}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    
    # Should fail without returning default data
    has_default_data = False
    if result.get('portfolio'):
        for entry in result['portfolio']:
            if entry.get('amount_invested') == 2554.80 or entry.get('buy_price') == 425.80:
                has_default_data = True
                break
    
    if not result['success'] and len(result.get('portfolio', [])) == 0:
        print("✅ Error case handled correctly - no default data returned")
        return True
    elif has_default_data:
        print("❌ Error case returned default data - this is the bug!")
        return False
    else:
        print("❌ Error case not handled correctly")
        return False

if __name__ == "__main__":
    print("Testing Portfolio Import - User's Exact Issue")
    print("=" * 60)
    
    test1 = test_user_spreadsheet_scenario()
    test2 = test_simple_columns_scenario()
    test3 = test_error_handling()
    
    print("\n" + "=" * 60)
    if all([test1, test2, test3]):
        print("🎉 ALL TESTS PASSED!")
        print("✅ Portfolio import now processes REAL data correctly")
        print("✅ No more default/mock data issues")
        print("✅ Works with both named columns and simple A,B,C,D format")
        print("✅ Error cases handled properly without default data fallback")
        print("\n🎯 The user's issue should now be completely resolved!")
    else:
        print("❌ Some tests failed. The issue may not be fully resolved.")
        print("❌ User may still see default data instead of their actual data.")
