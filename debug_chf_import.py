#!/usr/bin/env python3
"""
Debug script to test CHF currency import and session handling.
"""

import sys
import os
sys.path.append('.')

from app import app
import json

def test_chf_import():
    """Test CHF currency import flow."""
    
    print("🧪 Testing CHF Currency Import Flow")
    print("=" * 50)
    
    with app.test_request_context():
        from flask import session
        
        # Test 1: Simulate CHF portfolio import data
        print("\n1. Testing CHF Portfolio Import Data")
        
        chf_import_data = {
            'success': True,
            'portfolio': [
                {
                    'ticker': 'NESN.SW',
                    'amount_invested': 5000.0,
                    'buy_price': 100.0,
                    'shares': 50.0,
                    'currency': 'CHF',
                    'amount_invested_currency': 'CHF',
                    'buy_price_currency': 'CHF'
                },
                {
                    'ticker': 'NOVN.SW',
                    'amount_invested': 3000.0,
                    'buy_price': 75.0,
                    'shares': 40.0,
                    'currency': 'CHF',
                    'amount_invested_currency': 'CHF',
                    'buy_price_currency': 'CHF'
                }
            ],
            'cash_position': 1000.0,
            'detected_currency': 'CHF',
            'currency': 'CHF',
            'selected_currency': 'CHF'
        }
        
        print(f"   Import data currency: {chf_import_data.get('currency')}")
        print(f"   Detected currency: {chf_import_data.get('detected_currency')}")
        print(f"   Selected currency: {chf_import_data.get('selected_currency')}")
        
        # Test 2: Simulate confirm_portfolio_import logic
        print("\n2. Testing confirm_portfolio_import Logic")
        
        # Extract the key variables like the function does
        portfolio_entries = chf_import_data.get('portfolio', [])
        cash_position = chf_import_data.get('cash_position', 0.0)
        
        # CRITICAL FIX: Use detected_currency as primary source, not default to DKK
        detected_currency = chf_import_data.get('detected_currency', 'USD')
        source_currency = chf_import_data.get('currency', detected_currency)
        selected_currency = chf_import_data.get('selected_currency', detected_currency)
        
        print(f"   Processed detected_currency: {detected_currency}")
        print(f"   Processed source_currency: {source_currency}")
        print(f"   Processed selected_currency: {selected_currency}")
        
        # Test 3: Session storage
        print("\n3. Testing Session Storage")
        
        session['portfolio_currency'] = selected_currency
        session.modified = True
        
        stored_currency = session.get('portfolio_currency')
        print(f"   Stored portfolio_currency: {stored_currency}")
        
        # Test 4: Template rendering simulation
        print("\n4. Testing Template Data")
        
        template_data = {
            'portfolio_currency': session.get('portfolio_currency', 'USD')
        }
        
        print(f"   Template portfolio_currency: {template_data['portfolio_currency']}")
        
        # Test 5: Check if currency is being overridden somewhere
        print("\n5. Testing Currency Override Detection")
        
        if stored_currency != 'CHF':
            print(f"   ❌ ERROR: Currency was overridden! Expected CHF, got {stored_currency}")
        else:
            print(f"   ✅ SUCCESS: Currency correctly stored as CHF")
        
        print("\n" + "=" * 50)
        print("🎯 SUMMARY:")
        print(f"   Input Currency: CHF")
        print(f"   Detected Currency: {detected_currency}")
        print(f"   Selected Currency: {selected_currency}")
        print(f"   Stored Currency: {stored_currency}")
        print(f"   Template Currency: {template_data['portfolio_currency']}")
        
        if template_data['portfolio_currency'] == 'CHF':
            print("   ✅ SUCCESS: CHF currency flow working correctly!")
        else:
            print(f"   ❌ FAILURE: Expected CHF, got {template_data['portfolio_currency']}")

if __name__ == '__main__':
    test_chf_import()
