#!/usr/bin/env python3
"""
Simple test to verify delete functionality using the Flask app context.
"""

import sys
import os
sys.path.append('.')

def test_delete_functionality():
    """Test delete functionality directly."""
    
    print("🧪 Testing Delete Functionality Directly")
    print("=" * 50)
    
    try:
        from app import app, delete_stock_from_portfolio, fetch_portfolio_data, save_portfolio_data
        
        with app.test_request_context():
            from flask import session
            
            # Step 1: Create test portfolio
            print("\n1. Creating Test Portfolio")
            
            test_portfolio = [
                {
                    'ticker': 'AAPL',
                    'amount_invested': 1000.0,
                    'buy_price': 150.0,
                    'shares': 6.67,
                    'currency': 'USD'
                },
                {
                    'ticker': 'GOOGL',
                    'amount_invested': 2000.0,
                    'buy_price': 100.0,
                    'shares': 20.0,
                    'currency': 'USD'
                },
                {
                    'ticker': 'MSFT',
                    'amount_invested': 1500.0,
                    'buy_price': 300.0,
                    'shares': 5.0,
                    'currency': 'USD'
                }
            ]
            
            # Save test portfolio to session
            session['portfolio_data'] = test_portfolio
            session['portfolio_currency'] = 'USD'
            session.modified = True
            
            print(f"   ✅ Created portfolio with {len(test_portfolio)} stocks")
            
            # Step 2: Verify portfolio is saved
            print("\n2. Verifying Portfolio is Saved")
            
            portfolio_before = fetch_portfolio_data()
            print(f"   Portfolio has {len(portfolio_before)} stocks")
            print(f"   Stocks: {[stock.get('ticker') for stock in portfolio_before]}")
            
            if len(portfolio_before) == 3:
                print("   ✅ Portfolio correctly saved")
                
                # Step 3: Delete GOOGL
                print("\n3. Deleting GOOGL")
                
                success, message = delete_stock_from_portfolio('GOOGL')
                print(f"   Delete result: {success}, {message}")
                
                if success:
                    print("   ✅ Delete function returned success")
                    
                    # Step 4: Verify deletion
                    print("\n4. Verifying Deletion")
                    
                    portfolio_after = fetch_portfolio_data()
                    print(f"   Portfolio now has {len(portfolio_after)} stocks")
                    print(f"   Stocks: {[stock.get('ticker') for stock in portfolio_after]}")
                    
                    if len(portfolio_after) == 2 and 'GOOGL' not in [stock.get('ticker') for stock in portfolio_after]:
                        print("   ✅ SUCCESS: GOOGL was successfully deleted!")
                    else:
                        print("   ❌ FAILURE: GOOGL is still in the portfolio")
                        
                        # Check session directly
                        session_portfolio = session.get('portfolio_data', [])
                        print(f"   Session portfolio has {len(session_portfolio)} stocks")
                        print(f"   Session stocks: {[stock.get('ticker') for stock in session_portfolio]}")
                        
                else:
                    print(f"   ❌ Delete function failed: {message}")
                    
            else:
                print(f"   ❌ Portfolio not correctly saved, has {len(portfolio_before)} stocks instead of 3")
                
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎯 Direct Delete Test Completed!")

if __name__ == '__main__':
    test_delete_functionality()
