#!/usr/bin/env python3
"""
Debug the specific error with separated lines
"""

from portfolio_import import PortfolioImportService

def debug_separated_lines():
    """Debug the separated lines issue"""
    print("🔍 Debugging Separated Lines Issue")
    print("=" * 50)
    
    # The problematic text
    text = """
Alphabet Inc.
NasdaqGS:GOOGL
10
161
DKK 12,216.61

ASML Holding N.V.
NasdaqGS:ASML
2
668.5
DKK 9,279.65
"""
    
    print("Problematic text:")
    print(repr(text))
    print()
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    try:
        print("Processing...")
        result = service.extract_portfolio_from_text(text)
        print(f"Success: {result.success}")
        print(f"Entries: {len(result.portfolio_entries)}")
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: {entry.shares} @ {entry.buy_price}")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_separated_lines()
