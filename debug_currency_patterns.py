#!/usr/bin/env python3
"""
Debug the currency pattern matching issue
"""

import re

def test_currency_patterns():
    """Test the currency pattern matching logic"""
    
    test_text = """
Company | Value (¥) | Change % | Today | Price (USD)
Shopify Inc. | ¥1,803,220 | ▲ 13.71 % | ↑ 0.12 % | USD 85.77
Palantir Tech. | ¥704300 | ▼ 1.88% | → 0.00 % | $25.91
Roblox Corp. | ¥1 020 050 | ▲ 9.02% | ↑ 0.31% | 39.67 USD
Pinterest Inc. | ¥892,430 | ▲0.96 % | ↑ 0.06% | 43.11
Block Inc. | ¥2.370.100 | ▼ 4.20% | ↑ 0.21% | USD: 70.30
"""

    print("🔍 DEBUGGING CURRENCY PATTERN MATCHING")
    print("=" * 50)
    print(f"Test text:\n{test_text}")
    print("=" * 50)
    
    # Test JPY patterns
    print("\n1. JAPANESE YEN (¥) PATTERNS")
    print("-" * 30)
    
    yen_symbol = '¥'
    escaped_yen = re.escape(yen_symbol)
    
    # Portfolio value patterns
    portfolio_patterns = [
        f'{escaped_yen}[\\d,]+(?:,\\d{{3}})*(?:\\.\\d+)?',  # Large amounts with commas
        f'Value\\s*\\({escaped_yen}\\)',  # Column headers like "Value (¥)"
        f'Total\\s*{escaped_yen}[\\d,]+',  # Total amounts
    ]
    
    portfolio_matches = 0
    for pattern in portfolio_patterns:
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        print(f"Pattern '{pattern}': {len(matches)} matches - {matches}")
        portfolio_matches += len(matches)
    
    print(f"Total JPY portfolio matches: {portfolio_matches}")
    
    # Test USD patterns
    print("\n2. USD PATTERNS")
    print("-" * 30)
    
    # Stock price patterns for USD
    price_patterns = [
        f'USD\\s*:?\\s*[\\d.]+',  # USD: 85.77 or USD 85.77
        f'[\\d.]+\\s*USD',  # 85.77 USD
        f'Price\\s*\\(USD\\)',  # Column headers like "Price (USD)"
    ]
    
    price_matches = 0
    for pattern in price_patterns:
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        print(f"Pattern '{pattern}': {len(matches)} matches - {matches}")
        price_matches += len(matches)
    
    print(f"Total USD price matches: {price_matches}")
    
    # Test $ symbol patterns
    print("\n3. DOLLAR SYMBOL ($) PATTERNS")
    print("-" * 30)
    
    dollar_symbol = '$'
    escaped_dollar = re.escape(dollar_symbol)
    
    # Portfolio value patterns for $
    dollar_portfolio_patterns = [
        f'{escaped_dollar}[\\d,]+(?:,\\d{{3}})*(?:\\.\\d+)?',  # Large amounts with commas
        f'Value\\s*\\({escaped_dollar}\\)',  # Column headers like "Value ($)"
        f'Total\\s*{escaped_dollar}[\\d,]+',  # Total amounts
    ]
    
    dollar_portfolio_matches = 0
    for pattern in dollar_portfolio_patterns:
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        print(f"Pattern '{pattern}': {len(matches)} matches - {matches}")
        dollar_portfolio_matches += len(matches)
    
    print(f"Total $ portfolio matches: {dollar_portfolio_matches}")
    
    # Calculate weighted scores like the real algorithm
    print("\n4. WEIGHTED SCORING")
    print("-" * 30)
    
    # JPY scoring
    jpy_score = portfolio_matches * 6  # Portfolio values get 6x weight
    print(f"JPY score: {portfolio_matches} portfolio matches × 6 = {jpy_score}")
    
    # USD scoring  
    usd_score = price_matches * 4  # Price matches get 4x weight
    print(f"USD score: {price_matches} price matches × 4 = {usd_score}")
    
    # Dollar symbol scoring
    dollar_score = dollar_portfolio_matches * 6  # Portfolio values get 6x weight
    print(f"$ symbol score: {dollar_portfolio_matches} portfolio matches × 6 = {dollar_score}")
    
    total_usd_score = usd_score + dollar_score
    print(f"Total USD score: {usd_score} + {dollar_score} = {total_usd_score}")
    
    print(f"\nFinal comparison: JPY({jpy_score}) vs USD({total_usd_score})")
    
    if jpy_score > total_usd_score:
        print("✅ JPY should win!")
    else:
        print("❌ USD incorrectly wins!")

if __name__ == "__main__":
    test_currency_patterns()
