#!/usr/bin/env python3
"""
Test script to verify delete functionality is working correctly.
"""

import requests
import json
import time

def test_delete_functionality():
    """Test stock deletion functionality."""
    
    base_url = "http://127.0.0.1:9878"
    
    print("🧪 Testing Stock Delete Functionality")
    print("=" * 50)
    
    # Test 1: Add some test stocks first
    print("\n1. Adding Test Stocks")
    
    test_portfolio_data = {
        'success': True,
        'portfolio': [
            {
                'ticker': 'AAPL',
                'amount_invested': 1000.0,
                'buy_price': 150.0,
                'shares': 6.67,
                'currency': 'USD',
                'amount_invested_currency': 'USD',
                'buy_price_currency': 'USD',
                'purchase_date': '2024-01-15'
            },
            {
                'ticker': 'GOOGL',
                'amount_invested': 2000.0,
                'buy_price': 100.0,
                'shares': 20.0,
                'currency': 'USD',
                'amount_invested_currency': 'USD',
                'buy_price_currency': 'USD',
                'purchase_date': '2024-02-10'
            },
            {
                'ticker': 'MSFT',
                'amount_invested': 1500.0,
                'buy_price': 300.0,
                'shares': 5.0,
                'currency': 'USD',
                'amount_invested_currency': 'USD',
                'buy_price_currency': 'USD',
                'purchase_date': '2024-03-05'
            }
        ],
        'cash_position': 500.0,
        'detected_currency': 'USD',
        'currency': 'USD',
        'selected_currency': 'USD'
    }
    
    try:
        # Import test portfolio
        response = requests.post(
            f"{base_url}/api/import/confirm",
            json=test_portfolio_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Test portfolio imported: {result.get('message')}")
            
            # Test 2: Check portfolio before deletion
            print("\n2. Checking Portfolio Before Deletion")
            
            portfolio_response = requests.get(f"{base_url}/portfolio", timeout=30)
            
            if portfolio_response.status_code == 200:
                portfolio_html = portfolio_response.text
                
                # Check for stocks in HTML
                stocks_present = {
                    'AAPL': 'AAPL' in portfolio_html,
                    'GOOGL': 'GOOGL' in portfolio_html,
                    'MSFT': 'MSFT' in portfolio_html
                }
                
                print(f"   AAPL present: {'✅' if stocks_present['AAPL'] else '❌'}")
                print(f"   GOOGL present: {'✅' if stocks_present['GOOGL'] else '❌'}")
                print(f"   MSFT present: {'✅' if stocks_present['MSFT'] else '❌'}")
                
                if all(stocks_present.values()):
                    print("   ✅ All test stocks are present")
                    
                    # Test 3: Delete GOOGL stock
                    print("\n3. Deleting GOOGL Stock")
                    
                    # Use requests.Session to maintain cookies/session
                    session = requests.Session()
                    
                    # First get the portfolio page to establish session
                    session.get(f"{base_url}/portfolio", timeout=30)
                    
                    # Now try to delete GOOGL
                    delete_response = session.post(
                        f"{base_url}/delete_stock/GOOGL",
                        timeout=30,
                        allow_redirects=False  # Don't follow redirect to see the response
                    )
                    
                    print(f"   Delete response status: {delete_response.status_code}")
                    
                    if delete_response.status_code in [200, 302]:  # 302 is redirect after successful delete
                        print("   ✅ Delete request successful")
                        
                        # Test 4: Check portfolio after deletion
                        print("\n4. Checking Portfolio After Deletion")
                        
                        time.sleep(1)  # Give a moment for the change to take effect
                        
                        portfolio_after_response = session.get(f"{base_url}/portfolio", timeout=30)
                        
                        if portfolio_after_response.status_code == 200:
                            portfolio_after_html = portfolio_after_response.text
                            
                            # Check for stocks in HTML after deletion
                            stocks_after = {
                                'AAPL': 'AAPL' in portfolio_after_html,
                                'GOOGL': 'GOOGL' in portfolio_after_html,
                                'MSFT': 'MSFT' in portfolio_after_html
                            }
                            
                            print(f"   AAPL present: {'✅' if stocks_after['AAPL'] else '❌'}")
                            print(f"   GOOGL present: {'❌' if not stocks_after['GOOGL'] else '⚠️  STILL THERE!'}")
                            print(f"   MSFT present: {'✅' if stocks_after['MSFT'] else '❌'}")
                            
                            if not stocks_after['GOOGL'] and stocks_after['AAPL'] and stocks_after['MSFT']:
                                print("   ✅ SUCCESS: GOOGL was deleted, other stocks remain")
                            elif stocks_after['GOOGL']:
                                print("   ❌ FAILURE: GOOGL is still present after deletion")
                            else:
                                print("   ⚠️  PARTIAL: Some unexpected changes occurred")
                                
                        else:
                            print(f"   ❌ Portfolio page request failed after deletion: {portfolio_after_response.status_code}")
                            
                    else:
                        print(f"   ❌ Delete request failed: {delete_response.status_code}")
                        print(f"   Response: {delete_response.text}")
                        
                else:
                    print("   ❌ Not all test stocks are present, cannot test deletion")
                    
            else:
                print(f"   ❌ Portfolio page request failed: {portfolio_response.status_code}")
                
        else:
            print(f"   ❌ Test portfolio import failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Delete Functionality Test Completed!")
    print("\nTo test manually:")
    print("1. Go to http://127.0.0.1:9878/portfolio")
    print("2. Click the trash icon next to any stock")
    print("3. Confirm the deletion")
    print("4. Refresh the page")
    print("5. Check if the stock is still there")

if __name__ == '__main__':
    test_delete_functionality()
