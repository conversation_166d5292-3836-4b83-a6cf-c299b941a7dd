#!/usr/bin/env python3
"""
Test only the table format parsing to see what it extracts
"""

from portfolio_import import PortfolioImportService

def test_table_format_only():
    """Test only the table format parsing"""
    print("🔍 Testing Table Format Only")
    print("=" * 60)
    
    # This simulates what OCR should extract from the user's portfolio image
    simulated_ocr_text = """
    Ticker    Shares    Avg. Cost Basis    Market Value (DKK)    % Chg.
    
    Alphabet Inc.
    NasdaqGS:GOOGL    10    161    DKK 12,216.61    18.1%
    
    ASML Holding N.V.
    NasdaqGS:ASML    2    668.5    DKK 9,279.65    8.1%
    
    Uber Technologies, Inc.
    NYSE:UBER    10    74.59    DKK 5,851.40    22.1%
    
    Amazon.com, Inc.
    NasdaqGS:AMZN    8    186.92    DKK 11,790.22    22.7%
    """
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.12345678"
    
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)

    # Test only the table format parsing
    print("Testing table format parsing directly...")
    extractor = service.ai_extractor
    table_entries = extractor._parse_table_format(simulated_ocr_text)
    
    print(f"\nTable format found {len(table_entries)} entries:")
    for i, entry in enumerate(table_entries, 1):
        print(f"  {i}. {entry}")
    
    # Expected results
    expected_results = [
        {'ticker': 'GOOGL', 'shares': 10.0, 'buy_price': 161.0, 'amount_invested': 1610.0},
        {'ticker': 'ASML', 'shares': 2.0, 'buy_price': 668.5, 'amount_invested': 1337.0},
        {'ticker': 'UBER', 'shares': 10.0, 'buy_price': 74.59, 'amount_invested': 745.9},
        {'ticker': 'AMZN', 'shares': 8.0, 'buy_price': 186.92, 'amount_invested': 1495.36}
    ]
    
    print(f"\nExpected results:")
    for i, expected in enumerate(expected_results, 1):
        print(f"  {i}. {expected}")
    
    # Check if results match
    success = True
    if len(table_entries) != 4:
        print(f"\n❌ Expected 4 entries, got {len(table_entries)}")
        success = False
    
    for i, entry in enumerate(table_entries):
        if i < len(expected_results):
            expected = expected_results[i]
            if entry.get('ticker') != expected['ticker']:
                print(f"❌ Entry {i+1}: Expected ticker {expected['ticker']}, got {entry.get('ticker')}")
                success = False
            if abs(entry.get('shares', 0) - expected['shares']) > 0.1:
                print(f"❌ Entry {i+1}: Expected {expected['shares']} shares, got {entry.get('shares')}")
                success = False
            if abs(entry.get('buy_price', 0) - expected['buy_price']) > 0.1:
                print(f"❌ Entry {i+1}: Expected ${expected['buy_price']} price, got ${entry.get('buy_price')}")
                success = False
    
    if success:
        print("\n✅ SUCCESS: Table format parsing works correctly!")
    else:
        print("\n❌ ISSUE: Table format parsing has problems")
    
    return success

if __name__ == "__main__":
    test_table_format_only()
