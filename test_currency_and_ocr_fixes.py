#!/usr/bin/env python3
"""
Test script to verify the currency display and OCR error handling fixes.

This script tests:
1. Currency selection modal when AI detects mixed currencies
2. Proper currency display (DKK instead of USD)
3. Enhanced OCR failure guidance with helpful UI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService

def test_currency_selection_modal():
    """Test that currency selection modal appears when needed."""
    print("🧪 Testing Currency Selection Modal")
    print("=" * 50)
    
    # Simulate mixed currency portfolio text
    mixed_currency_text = """
    My Investment Portfolio
    
    AAPL    $2,500.00 USD    $125.00    20 shares
    NOVO    1,200.00 DKK     150.00 kr  8 shares  
    MSFT    €1,800.00        €180.00    10 shares
    """
    
    service = PortfolioImportService("test_key", "test_key")
    result = service.extract_portfolio_from_text(mixed_currency_text)
    
    print(f"✅ Extraction successful: {result.success}")
    print(f"📊 Portfolio entries found: {len(result.portfolio_entries)}")
    
    # Check if currency selection is required
    api_response = service.format_for_api(result)
    currency_info = api_response.get('currency_info', {})
    
    print(f"💱 Currency Selection Required: {currency_info.get('requires_user_selection', False)}")
    print(f"🌍 Detected Currencies: {currency_info.get('detected_currencies', [])}")
    
    if currency_info.get('gemini_question'):
        print(f"🤖 Gemini AI Question: {currency_info['gemini_question']}")
    
    return api_response

def test_dkk_currency_display():
    """Test that DKK currency is properly detected and displayed."""
    print("\n🧪 Testing DKK Currency Display")
    print("=" * 50)
    
    # Simulate DKK portfolio (like user's image)
    dkk_portfolio_text = """
    Aktieportefølje
    
    Ticker    Markedsværdi    GAK        Antal
    AAPL      2.462,85 DKK    161,61 kr  13 stk
    MSFT      3.250,75 DKK    325,08 kr  10 stk
    GOOGL     1.890,50 DKK    189,05 kr  10 stk
    """
    
    service = PortfolioImportService("test_key", "test_key")
    result = service.extract_portfolio_from_text(dkk_portfolio_text)
    
    print(f"✅ Extraction successful: {result.success}")
    print(f"📊 Portfolio entries found: {len(result.portfolio_entries)}")
    
    # Check detected currency
    api_response = service.format_for_api(result)
    detected_currency = api_response.get('detected_currency', 'Unknown')
    
    print(f"💰 Detected Currency: {detected_currency}")
    
    # Check individual entries
    for i, entry in enumerate(result.portfolio_entries):
        print(f"  Entry {i+1}: {entry.ticker}")
        print(f"    Amount Invested: {entry.amount_invested} {entry.currency}")
        print(f"    Buy Price: {entry.buy_price} {entry.buy_price_currency}")
        print(f"    Shares: {entry.shares}")
    
    # Verify currency is DKK, not USD
    if detected_currency == 'DKK':
        print("✅ SUCCESS: DKK currency properly detected!")
    else:
        print(f"❌ ISSUE: Expected DKK, got {detected_currency}")
    
    return api_response

def test_ocr_failure_guidance():
    """Test enhanced OCR failure guidance."""
    print("\n🧪 Testing OCR Failure Guidance")
    print("=" * 50)
    
    # Simulate OCR failure
    ocr_failed_text = "OCR_EXTRACTION_FAILED: Unable to extract text from image."
    
    service = PortfolioImportService("test_key", "test_key")
    result = service.extract_portfolio_from_text(ocr_failed_text)
    
    print(f"❌ Extraction failed (expected): {not result.success}")
    print(f"📝 Error messages: {len(result.errors)}")
    
    # Check for enhanced guidance
    api_response = service.format_for_api(result)
    user_guidance = api_response.get('raw_data', {}).get('user_guidance', {})
    
    if user_guidance.get('show_modal'):
        print("✅ Enhanced OCR failure modal will be shown")
        print(f"   Title: {user_guidance.get('title')}")
        print(f"   Suggestions: {len(user_guidance.get('suggestions', []))}")
        print(f"   Alternative action: {user_guidance.get('alternative_action', {}).get('text')}")
    else:
        print("❌ No enhanced guidance found")
    
    return api_response

def test_currency_conversion():
    """Test currency conversion logic."""
    print("\n🧪 Testing Currency Conversion")
    print("=" * 50)
    
    # Test DKK to USD conversion (approximately 6.9 DKK per USD)
    dkk_amount = 2462.85  # From user's example
    expected_usd = dkk_amount * 0.145  # DKK rate
    
    print(f"💰 DKK Amount: {dkk_amount} kr")
    print(f"💵 Expected USD: ${expected_usd:.2f}")
    print(f"📊 Conversion Rate: 1 DKK = 0.145 USD (approx 6.9 DKK per USD)")
    
    # Verify the rate is reasonable
    if 350 <= expected_usd <= 400:  # Should be around $357
        print("✅ Currency conversion rate looks correct")
    else:
        print(f"❌ Currency conversion might be off: ${expected_usd:.2f}")

def main():
    """Run all tests."""
    print("🚀 CURRENCY AND OCR FIXES TEST SUITE")
    print("=" * 60)
    print("Testing the fixes for:")
    print("1. Currency display issue (DKK showing as USD)")
    print("2. OCR extraction failure guidance")
    print("3. Currency selection modal for mixed currencies")
    print("=" * 60)
    
    try:
        # Test 1: Currency selection modal
        mixed_result = test_currency_selection_modal()
        
        # Test 2: DKK currency display
        dkk_result = test_dkk_currency_display()
        
        # Test 3: OCR failure guidance
        ocr_result = test_ocr_failure_guidance()
        
        # Test 4: Currency conversion
        test_currency_conversion()
        
        print("\n" + "=" * 60)
        print("🎉 TEST SUMMARY")
        print("=" * 60)
        
        # Check results
        currency_selection_works = mixed_result.get('currency_info', {}).get('requires_user_selection', False)
        dkk_detected = dkk_result.get('detected_currency') == 'DKK'
        ocr_guidance_works = ocr_result.get('raw_data', {}).get('user_guidance', {}).get('show_modal', False)
        
        print(f"✅ Currency Selection Modal: {'WORKING' if currency_selection_works else 'NEEDS WORK'}")
        print(f"✅ DKK Currency Detection: {'WORKING' if dkk_detected else 'NEEDS WORK'}")
        print(f"✅ OCR Failure Guidance: {'WORKING' if ocr_guidance_works else 'NEEDS WORK'}")
        
        if currency_selection_works and dkk_detected and ocr_guidance_works:
            print("\n🎉 ALL FIXES ARE WORKING!")
            print("✅ Users will see proper currency (DKK, not USD)")
            print("✅ Currency selection modal appears when needed")
            print("✅ Helpful guidance shown when OCR fails")
        else:
            print("\n⚠️  Some fixes need attention")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()