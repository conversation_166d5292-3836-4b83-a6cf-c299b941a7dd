#!/usr/bin/env python3
"""
Test Google Vision API directly with the user's image
"""

import requests
import base64
import json

def test_google_vision_api():
    """Test Google Vision API with a simple image"""
    
    # Create a simple test image (1x1 PNG with some text-like data)
    # This is just to test if the API is working
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    try:
        print("Testing Google Vision API...")
        print(f"API Key: {google_vision_api_key[:10]}...")
        
        # Encode image data to base64
        image_base64 = base64.b64encode(test_image_data).decode('utf-8')
        
        # Prepare request for Google Vision API
        url = f"https://vision.googleapis.com/v1/images:annotate?key={google_vision_api_key}"
        
        payload = {
            "requests": [
                {
                    "image": {
                        "content": image_base64
                    },
                    "features": [
                        {
                            "type": "TEXT_DETECTION",
                            "maxResults": 50
                        }
                    ]
                }
            ]
        }
        
        print("Sending request to Google Vision API...")
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("API Response:")
            print(json.dumps(result, indent=2))
            
            if 'responses' in result and result['responses']:
                if 'error' in result['responses'][0]:
                    error_msg = result['responses'][0]['error'].get('message', 'Unknown API error')
                    print(f"❌ Vision API error: {error_msg}")
                else:
                    text_annotations = result['responses'][0].get('textAnnotations', [])
                    if text_annotations:
                        extracted_text = text_annotations[0].get('description', '')
                        print(f"✅ Successfully extracted text: '{extracted_text}'")
                    else:
                        print("✅ API working but no text found in test image (expected)")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response text: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_google_vision_api()
