import requests
import numpy as np

# FinancialModelingPrep API
API_KEY = "********************************"
BASE_URL = "https://financialmodelingprep.com/api/v3/"

def fetch_company_data(symbol):
    # Fetch the financials for the company
    url = f"{BASE_URL}financials/income-statement/{symbol}?apikey={API_KEY}"
    response = requests.get(url)
    data = response.json()
    
    if "financials" not in data:
        print("Error: Unable to fetch company data.")
        return None
    
    return data["financials"][0]

def fetch_cash_flow(symbol):
    # Fetch cash flow data (for Free Cash Flow)
    url = f"{BASE_URL}cash-flow-statement/{symbol}?apikey={API_KEY}"
    response = requests.get(url)
    data = response.json()
    
    if "financials" not in data:
        print("Error: Unable to fetch cash flow data.")
        return None
    
    return data["financials"][0]

def discounted_cash_flow(symbol, years, discount_rate, growth_rate, margin_of_safety):
    # Fetch required data
    income_data = fetch_company_data(symbol)
    cash_flow_data = fetch_cash_flow(symbol)
    
    if not income_data or not cash_flow_data:
        return None
    
    # Extract Free Cash Flow (FCF) and other relevant values
    free_cash_flow = float(cash_flow_data["freeCashFlow"])
    perpetuity_growth_rate = 0.025  # 2.5% perpetual growth rate
    
    # Forecast free cash flow for the number of years
    forecasted_cash_flows = []
    for year in range(1, years + 1):
        forecasted_cash_flows.append(free_cash_flow * (1 + growth_rate / 100) ** year)
    
    # Calculate terminal value (TV) after the forecast period using the perpetuity formula
    terminal_value = forecasted_cash_flows[-1] * (1 + perpetuity_growth_rate) / (discount_rate / 100 - perpetuity_growth_rate)
    
    # Calculate the present value of future cash flows and terminal value
    total_dcf = 0
    for i in range(years):
        total_dcf += forecasted_cash_flows[i] / (1 + discount_rate / 100) ** (i + 1)
    
    terminal_value_discounted = terminal_value / (1 + discount_rate / 100) ** years
    total_dcf += terminal_value_discounted
    
    # Apply Margin of Safety
    total_dcf *= (1 - margin_of_safety / 100)
    
    return total_dcf

def main():
    # User Inputs
    symbol = input("Enter the stock symbol (e.g., AAPL, MSFT): ").upper()
    years = int(input("Enter the number of years for forecasting (e.g., 5, 10): "))
    discount_rate = float(input("Enter the discount rate (as a percentage, e.g., 10): "))
    growth_rate = float(input("Enter the growth rate (as a percentage, e.g., 5): "))
    margin_of_safety = float(input("Enter the margin of safety (as a percentage, e.g., 20): "))
    
    # Perform DCF calculation
    dcf_value = discounted_cash_flow(symbol, years, discount_rate, growth_rate, margin_of_safety)
    
    if dcf_value is not None:
        print(f"The discounted cash flow (DCF) value for {symbol} is: ${dcf_value:,.2f}")
    else:
        print("Unable to calculate DCF.")

if __name__ == "__main__":
    main()