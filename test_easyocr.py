#!/usr/bin/env python3
"""
Test EasyOCR directly
"""

import easyocr
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_test_portfolio_image():
    """Create a test image with portfolio data"""
    
    # Create a white image
    width, height = 800, 400
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # Try to use a default font
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # Draw portfolio data similar to the user's image
    portfolio_text = [
        "Ticker    Shares    Avg. Cost    Market Value (DKK)    % Chg.",
        "GOOGL     10        161          DKK 12,216.61         18.1%",
        "ASML      2         668.5        DKK 9,279.65          8.1%", 
        "UBER      10        74.59        DKK 5,851.40          22.1%",
        "AMZN      8         186.92       DKK 11,790.22         22.7%"
    ]
    
    y_position = 50
    for line in portfolio_text:
        draw.text((50, y_position), line, fill='black', font=font)
        y_position += 30
    
    return image

def test_easyocr():
    """Test EasyOCR with portfolio image"""
    
    try:
        print("Creating test portfolio image...")
        image = create_test_portfolio_image()
        
        # Save for debugging
        image.save('test_portfolio_image_easyocr.png')
        print("Saved test image as test_portfolio_image_easyocr.png")
        
        print("Initializing EasyOCR reader...")
        reader = easyocr.Reader(['en'])
        
        print("Testing EasyOCR...")
        
        # Convert PIL image to numpy array for easyocr
        img_array = np.array(image)
        
        print("Running OCR...")
        results = reader.readtext(img_array)
        
        print(f"EasyOCR found {len(results)} text regions")
        
        if results:
            # Combine all detected text
            extracted_texts = []
            for result in results:
                bbox, text, confidence = result
                print(f"Text: '{text}' (confidence: {confidence:.2f})")
                if confidence > 0.5:  # Only include high-confidence text
                    extracted_texts.append(text)
            
            if extracted_texts:
                full_text = '\n'.join(extracted_texts)
                print(f"\n✅ Successfully extracted text:")
                print(f"'{full_text}'")
                return full_text
            else:
                print("❌ No high-confidence text found")
                return None
        else:
            print("❌ No text detected")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_easyocr()
    if result:
        print(f"\n✅ Final result: {result}")
    else:
        print("\n❌ No text extracted")
