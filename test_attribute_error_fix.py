#!/usr/bin/env python3
"""
Test the fix for the 'PortfolioImportService' object has no attribute 'detected_currency' error
"""

import os
import sys

# Set the Google API key for testing
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'

from portfolio_import import PortfolioImportService

def test_attribute_error_fix():
    """Test that the AttributeError is fixed"""
    print("Testing AttributeError Fix")
    print("=" * 50)
    
    # Test data that was causing the error
    test_text = """
    Portfolio Overview
    
    GOOGL - Alphabet Inc
    Current Value: 15848 DKK
    
    AMZN - Amazon Inc  
    Current Value: 18858 DKK
    
    ASML - ASML Holding
    Current Value: 31962 DKK
    
    UBER - Uber Technologies
    Current Value: 5886 DKK
    """
    
    # Initialize the service
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    print("Testing PortfolioImportService.extract_portfolio_from_text()...")
    
    try:
        # This should not raise an AttributeError anymore
        result = service.extract_portfolio_from_text(test_text)
        
        print(f"✅ SUCCESS: No AttributeError raised!")
        print(f"Result success: {result.success}")
        print(f"Portfolio entries: {len(result.portfolio_entries)}")
        print(f"Errors: {result.errors}")
        print(f"Warnings: {result.warnings}")
        
        # Check if detected_currency attribute exists
        if hasattr(service, 'detected_currency'):
            print(f"✅ Service has detected_currency attribute: {service.detected_currency}")
        else:
            print(f"❌ Service missing detected_currency attribute")
        
        # Check if AI extractor has detected_currency
        if hasattr(service.ai_extractor, 'detected_currency'):
            print(f"✅ AI extractor has detected_currency: {service.ai_extractor.detected_currency}")
        else:
            print(f"❌ AI extractor missing detected_currency")
        
        # Show portfolio entries if any
        if result.portfolio_entries:
            print(f"\nPortfolio entries found:")
            for i, entry in enumerate(result.portfolio_entries, 1):
                print(f"{i}. {entry.ticker}: {entry.current_value} {entry.currency}")
        else:
            print(f"\nNo portfolio entries found (this is expected for this test)")
            
    except AttributeError as e:
        print(f"❌ FAILED: AttributeError still occurs: {e}")
        import traceback
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"⚠️  Other error occurred: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_currency_detection_integration():
    """Test that currency detection works properly in the service"""
    print("\n" + "=" * 50)
    print("Testing Currency Detection Integration")
    print("=" * 50)
    
    # Test with Euro data
    euro_text = """
    Portfolio Overview
    
    AAPL - Apple Inc
    Amount Invested: €2,100.93
    Current Value: €2,462.85
    
    TSLA - Tesla Inc  
    Amount Invested: €1,850.00
    Current Value: €1,890.50
    """
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    print("Testing with Euro currency data...")
    
    try:
        result = service.extract_portfolio_from_text(euro_text)
        
        print(f"Service detected currency: {service.detected_currency}")
        print(f"AI extractor detected currency: {service.ai_extractor.detected_currency}")
        
        if service.detected_currency == 'EUR':
            print("✅ Currency detection working correctly")
        else:
            print(f"⚠️  Expected EUR, got {service.detected_currency}")
            
    except Exception as e:
        print(f"❌ Error in currency detection test: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success1 = test_attribute_error_fix()
    success2 = test_currency_detection_integration()
    
    if success1 and success2:
        print(f"\n🎉 ALL TESTS PASSED! The AttributeError has been fixed.")
    else:
        print(f"\n❌ Some tests failed. Please check the output above.")
