#!/usr/bin/env python3
"""
Currency Bug Detector - Comprehensive debugging tool to find currency preservation issues
"""

import json
import logging
from portfolio_import import process_image_upload, AIPortfolioExtractor
import os

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CurrencyBugDetector:
    def __init__(self):
        self.google_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
        self.eodhd_api_key = None
        
    def test_currency_detection_pipeline(self, test_text: str, user_portfolio_currency: str = 'USD'):
        """Test the entire currency detection pipeline step by step"""
        
        print("=" * 80)
        print("🔍 CURRENCY BUG DETECTOR - COMPREHENSIVE ANALYSIS")
        print("=" * 80)
        
        print(f"📝 Test Input Text: {test_text[:200]}...")
        print(f"👤 User Portfolio Currency: {user_portfolio_currency}")
        print()
        
        # Step 1: Test AI Extractor directly
        print("🤖 STEP 1: Testing AI Extractor Currency Detection")
        print("-" * 50)
        
        ai_extractor = AIPortfolioExtractor(self.google_api_key, self.eodhd_api_key)
        ai_extractor.user_portfolio_currency = user_portfolio_currency
        
        # Test currency detection
        detected_currency = ai_extractor._detect_primary_currency(test_text)
        print(f"🌍 Detected Primary Currency: {detected_currency}")
        
        # Test Gemini AI extraction
        print("\n🧠 STEP 2: Testing Gemini AI Extraction")
        print("-" * 50)
        
        try:
            ai_result = ai_extractor.extract_portfolio_data_with_ai(test_text)
            print(f"✅ Gemini AI Success: {ai_result.get('success', False)}")
            
            if ai_result.get('success'):
                portfolio_data = ai_result.get('portfolio', [])
                print(f"📊 Number of entries extracted: {len(portfolio_data)}")
                
                for i, entry in enumerate(portfolio_data):
                    print(f"\n📈 Entry {i+1}:")
                    print(f"   🏷️  Ticker: {entry.get('ticker', 'N/A')}")
                    print(f"   💰 Amount Invested: {entry.get('amount_invested', 'N/A')}")
                    print(f"   💰 Amount Invested Currency: {entry.get('amount_invested_currency', 'NOT_SET')}")
                    print(f"   💵 Buy Price: {entry.get('buy_price', 'N/A')}")
                    print(f"   💵 Buy Price Currency: {entry.get('buy_price_currency', 'NOT_SET')}")
                    print(f"   🌍 General Currency: {entry.get('currency', 'NOT_SET')}")
                    
                    # Check for currency issues
                    amount_currency = entry.get('amount_invested_currency')
                    buy_currency = entry.get('buy_price_currency')
                    general_currency = entry.get('currency')
                    
                    print(f"\n   🔍 CURRENCY ANALYSIS:")
                    if amount_currency == 'USD' and detected_currency != 'USD':
                        print(f"   ❌ BUG DETECTED: amount_invested_currency is USD but detected currency is {detected_currency}")
                    elif amount_currency == detected_currency:
                        print(f"   ✅ GOOD: amount_invested_currency matches detected currency ({amount_currency})")
                    else:
                        print(f"   ⚠️  MISMATCH: amount_invested_currency={amount_currency}, detected={detected_currency}")
                        
            else:
                print(f"❌ Gemini AI Failed: {ai_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Gemini AI Exception: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n🔧 STEP 3: Testing Full Pipeline")
        print("-" * 50)
        
        # Create a test image data (dummy bytes)
        test_image_data = b"dummy_image_data"
        
        # Mock the image processing to use our test text
        original_extract_text = ai_extractor.extract_text_from_image
        
        def mock_extract_text(image_data):
            return test_text
            
        # Temporarily replace the method
        ai_extractor.extract_text_from_image = mock_extract_text
        
        try:
            # Test the full pipeline
            result = process_image_upload(
                image_data=test_image_data,
                google_vision_api_key=self.google_api_key,
                eodhd_api_key=self.eodhd_api_key,
                user_portfolio_currency=user_portfolio_currency
            )
            
            print(f"✅ Full Pipeline Success: {result.get('success', False)}")
            
            if result.get('success'):
                portfolio = result.get('portfolio', [])
                print(f"📊 Final Portfolio Entries: {len(portfolio)}")
                
                for i, entry in enumerate(portfolio):
                    print(f"\n📈 Final Entry {i+1}:")
                    print(f"   🏷️  Ticker: {entry.get('ticker', 'N/A')}")
                    print(f"   💰 Amount Invested: {entry.get('amount_invested', 'N/A')}")
                    print(f"   💰 Amount Invested Currency: {entry.get('amount_invested_currency', 'NOT_SET')}")
                    print(f"   💵 Buy Price: {entry.get('buy_price', 'N/A')}")
                    print(f"   💵 Buy Price Currency: {entry.get('buy_price_currency', 'NOT_SET')}")
                    print(f"   🌍 Currency: {entry.get('currency', 'NOT_SET')}")
                    
                    # Final bug check
                    final_amount_currency = entry.get('amount_invested_currency')
                    if final_amount_currency == 'USD' and detected_currency != 'USD':
                        print(f"   🚨 FINAL BUG CONFIRMED: Currency should be {detected_currency} but is {final_amount_currency}")
                        print(f"   🔧 RECOMMENDATION: Check currency assignment in _process_gemini_response method")
                    elif final_amount_currency == detected_currency:
                        print(f"   ✅ FINAL RESULT GOOD: Currency preserved correctly ({final_amount_currency})")
                    else:
                        print(f"   ⚠️  FINAL MISMATCH: Expected {detected_currency}, got {final_amount_currency}")
                        
            else:
                print(f"❌ Full Pipeline Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Full Pipeline Exception: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # Restore original method
            ai_extractor.extract_text_from_image = original_extract_text
            
        print("\n" + "=" * 80)
        print("🎯 SUMMARY AND RECOMMENDATIONS")
        print("=" * 80)
        print("If bugs were detected above, the issue is likely in:")
        print("1. Gemini AI prompt not requesting amount_invested_currency")
        print("2. _process_gemini_response not preserving the detected currency")
        print("3. Currency conversion logic overriding the detected currency")
        print("4. Frontend not displaying the correct currency data")

def test_danish_portfolio():
    """Test with Danish portfolio data"""
    
    detector = CurrencyBugDetector()
    
    # Test with Danish text that should detect DKK
    danish_test_text = """
    Portfolio Overview - Danske Bank
    
    GOOGL - Google Inc.
    Antal aktier: 11,96
    GAK (køb): 1.195,00 kr
    Markedsværdi: 14.300,00 kr
    
    AMZN - Amazon
    Antal aktier: 11,98  
    GAK (køb): 1.344,00 kr
    Markedsværdi: 16.100,00 kr
    
    ASML - ASML Holding
    Antal aktier: 6,45
    GAK (køb): 5.175,00 kr
    Markedsværdi: 33.400,00 kr
    """
    
    print("🇩🇰 Testing Danish Portfolio (should detect DKK)")
    detector.test_currency_detection_pipeline(danish_test_text, user_portfolio_currency='USD')

if __name__ == "__main__":
    test_danish_portfolio()
