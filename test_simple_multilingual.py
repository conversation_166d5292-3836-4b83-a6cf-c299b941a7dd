#!/usr/bin/env python3
"""
Simple test for multilingual false positive filtering
"""

from portfolio_import import PortfolioImportService

def test_simple():
    print("🌍 Testing Universal Multilingual False Positive Filtering")
    print("=" * 70)
    
    # Simple test case - French text with MSFT ticker
    french_text = """
    MSFT
    Mes participations
    Quantité: 5 pcs
    Prix: 300,00 EUR
    Valeur: 1.500,00 EUR
    """
    
    print("Testing French text:")
    print(french_text)
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.12345678"
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    
    result = service.extract_portfolio_from_text(french_text)
    
    print(f"Success: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    
    if result.portfolio_entries:
        for entry in result.portfolio_entries:
            print(f"  Ticker: {entry.ticker}")
    
    # Check for false positives
    found_tickers = [entry.ticker for entry in result.portfolio_entries]
    false_positives = ["PCS", "PRIX", "EUR", "VALEUR"]
    found_false_positives = [fp for fp in false_positives if fp in found_tickers]
    
    if "MSFT" in found_tickers and not found_false_positives:
        print("✅ SUCCESS: French test passed!")
        print("✅ Found MSFT, no false positives")
        return True
    else:
        print("❌ FAILED:")
        if "MSFT" not in found_tickers:
            print("❌ Missing MSFT")
        if found_false_positives:
            print(f"❌ False positives: {found_false_positives}")
        return False

if __name__ == "__main__":
    success = test_simple()
    if success:
        print("\n🎉 UNIVERSAL LANGUAGE FILTERING WORKS!")
    else:
        print("\n❌ NEEDS IMPROVEMENT")
