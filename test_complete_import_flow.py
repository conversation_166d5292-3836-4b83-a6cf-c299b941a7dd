#!/usr/bin/env python3
"""
Test the complete portfolio import flow to identify currency display issues
"""

import sys
sys.path.append('.')
from portfolio_import import PortfolioImportService

def simulate_complete_import_flow():
    """Simulate the complete import flow from extraction to final display"""
    
    print("=== COMPLETE IMPORT FLOW SIMULATION ===")
    
    # Step 1: Extract portfolio data
    dkk_text = '''
    Versus Systems Inc.
    NasdaqGS:VS
    Antal: 13 stk
    GAK: 161,61 USD
    Markedsværdi: 2.462,85 USD
    '''
    
    service = PortfolioImportService('test_key', 'test_key')
    result = service.extract_portfolio_from_text(dkk_text)
    api_response = service.format_for_api(result)
    
    print("📤 Step 1: Extraction Results")
    print(f"   Success: {api_response.get('success')}")
    print(f"   Detected Currency: {api_response.get('detected_currency')}")
    print(f"   Requires Selection: {api_response.get('currency_info', {}).get('requires_user_selection')}")
    
    if api_response.get('portfolio'):
        entry = api_response['portfolio'][0]
        print(f"   Entry: {entry.get('ticker')} - {entry.get('amount_invested')} {entry.get('currency')}")
    
    # Step 2: Simulate user selecting DKK
    print("\n📥 Step 2: User Selects DKK")
    
    # This simulates what confirm_portfolio_import would do
    selected_currency = 'DKK'
    source_currency = api_response.get('detected_currency', 'USD')
    
    print(f"   Source Currency: {source_currency}")
    print(f"   Selected Currency: {selected_currency}")
    
    # Currency conversion rates (same as in app.py)
    currency_rates = {
        'USD': 1.0, 'EUR': 1.08, 'GBP': 1.27, 'JPY': 0.0067, 'CAD': 0.74, 'AUD': 0.66,
        'CHF': 1.10, 'CNY': 0.14, 'SEK': 0.092, 'NOK': 0.091, 'DKK': 0.145,
        'PLN': 0.25, 'CZK': 0.044, 'HUF': 0.0027, 'BRL': 0.20, 'MXN': 0.059,
        'INR': 0.012, 'KRW': 0.00076, 'SGD': 0.74, 'HKD': 0.13, 'NZD': 0.61,
        'ZAR': 0.055, 'RUB': 0.011, 'TRY': 0.034, 'THB': 0.028, 'MYR': 0.22,
        'IDR': 0.000066, 'PHP': 0.018, 'ILS': 0.27, 'VND': 0.000041
    }
    
    def convert_to_usd(amount, from_currency):
        """Convert amount from source currency to USD."""
        if from_currency == 'USD':
            return amount
        rate = currency_rates.get(from_currency, 1.0)
        return amount * rate
    
    # Step 3: Simulate currency conversion logic
    print("\n🔄 Step 3: Currency Conversion")
    
    if api_response.get('portfolio'):
        entry = api_response['portfolio'][0]
        original_amount = entry.get('amount_invested', 0)
        original_price = entry.get('buy_price', 0)
        
        print(f"   Original Amount: {original_amount} {source_currency}")
        print(f"   Original Price: {original_price} {source_currency}")
        
        # Convert to USD first (if not already USD)
        amount_usd = convert_to_usd(original_amount, source_currency)
        price_usd = convert_to_usd(original_price, source_currency)
        
        print(f"   USD Amount: {amount_usd} USD")
        print(f"   USD Price: {price_usd} USD")
        
        # Convert from USD to selected currency
        selected_rate = currency_rates.get(selected_currency, 1.0)
        amount_display = amount_usd / selected_rate if selected_rate != 0 else amount_usd
        price_display = price_usd / selected_rate if selected_rate != 0 else price_usd
        
        print(f"   DKK Rate: {selected_rate} (1 DKK = {selected_rate} USD)")
        print(f"   Final Amount: {amount_display} {selected_currency}")
        print(f"   Final Price: {price_display} {selected_currency}")
        
        # Step 4: Check if conversion makes sense
        print("\n✅ Step 4: Validation")
        
        # Expected: 1 USD ≈ 6.9 DKK, so DKK amounts should be ~6.9x larger
        expected_multiplier = 1 / selected_rate  # Should be ~6.9
        print(f"   Expected multiplier (USD to DKK): {expected_multiplier:.2f}")
        
        actual_multiplier = amount_display / original_amount if original_amount > 0 else 0
        print(f"   Actual multiplier: {actual_multiplier:.2f}")
        
        if abs(actual_multiplier - expected_multiplier) < 0.1:
            print("   ✅ Currency conversion looks correct")
        else:
            print("   ❌ Currency conversion might be wrong")
            
        # Check for reasonable ranges
        if 10000 <= amount_display <= 20000:  # ~$2100 * 6.9 ≈ 14500 DKK
            print("   ✅ Amount is in reasonable DKK range")
        else:
            print(f"   ❌ Amount seems unreasonable: {amount_display}")
            
        if 1000 <= price_display <= 1200:  # ~$161 * 6.9 ≈ 1110 DKK
            print("   ✅ Price is in reasonable DKK range")
        else:
            print(f"   ❌ Price seems unreasonable: {price_display}")
            
        # Step 5: Simulate final portfolio entry
        print("\n🎯 Step 5: Final Portfolio Entry")
        final_entry = {
            'ticker': entry.get('ticker'),
            'shares': entry.get('shares'),
            'amount_invested': amount_display,
            'buy_price': price_display,
            'currency': selected_currency
        }
        
        print(f"   Final Entry: {final_entry}")
        
        # This is what should appear in the portfolio table
        print(f"\n📊 Expected Portfolio Display:")
        print(f"   Ticker: {final_entry['ticker']}")
        print(f"   Amount Invested: {final_entry['amount_invested']:.2f} kr")
        print(f"   Buy Price: {final_entry['buy_price']:.2f} kr")
        print(f"   Shares: {final_entry['shares']}")

if __name__ == "__main__":
    simulate_complete_import_flow()