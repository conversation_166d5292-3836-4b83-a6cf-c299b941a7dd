#!/usr/bin/env python3
"""
Debug spreadsheet processing to see why entries aren't being created
"""

import io
import csv
import pandas as pd
from portfolio_import import PortfolioImportService

def debug_spreadsheet_processing():
    """Debug the spreadsheet processing step by step"""
    print("🔍 Debug Spreadsheet Processing")
    print("=" * 50)
    
    # Create a simple DKK CSV
    dkk_csv = """Ticker,Shares,Buy Price,Current Value
NOVO,100,850.50 kr,92075 kr
ORSTED,50,450.25 kr,24265 kr"""
    
    print("📄 Input CSV:")
    print(dkk_csv)
    print()
    
    # Convert to DataFrame to see what pandas sees
    csv_io = io.StringIO(dkk_csv)
    df = pd.read_csv(csv_io)
    
    print("📊 Pandas DataFrame:")
    print(df)
    print()
    print("📊 DataFrame Info:")
    print(f"Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"Data types: {df.dtypes.to_dict()}")
    print()
    
    # Test the service
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    print("🔧 Testing extract_portfolio_from_spreadsheet...")
    result = service.extract_portfolio_from_spreadsheet(df)
    
    print(f"Success: {result.success}")
    print(f"Entries: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    if result.portfolio_entries:
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: {entry.amount_invested} {entry.currency}, shares: {entry.shares}")
    
    print("\n🔧 Testing currency detection...")
    spreadsheet_text = ""
    for col in df.columns:
        spreadsheet_text += f"{col} "
        for val in df[col].dropna().head(10):
            spreadsheet_text += f"{val} "
    
    print(f"Spreadsheet text: '{spreadsheet_text}'")
    detected_currency = service.ai_extractor._detect_primary_currency(spreadsheet_text)
    print(f"Detected currency: {detected_currency}")
    
    print("\n🔧 Testing row processing...")
    for index, row in df.iterrows():
        print(f"Row {index}: {dict(row)}")
        
        # Test currency detection for this row
        row_text = " ".join([str(val) for val in row.values if pd.notna(val)])
        row_currency = service.ai_extractor._detect_primary_currency(row_text)
        print(f"  Row text: '{row_text}'")
        print(f"  Row currency: {row_currency}")
        
        # Test numeric cleaning
        for col, val in row.items():
            if pd.notna(val):
                cleaned = service._clean_numeric_value(val)
                print(f"  {col}: '{val}' → {cleaned}")

if __name__ == "__main__":
    debug_spreadsheet_processing()
