<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .upload-area {
            border: 2px dashed #666;
            border-radius: 12px;
            padding: 48px 24px;
            margin: 24px 0;
            text-align: center;
            cursor: pointer;
            background: #2a2a2a;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #f59e0b;
            background: rgba(245, 158, 11, 0.05);
            transform: scale(1.02);
        }
        
        .upload-area:active {
            transform: scale(0.98);
            background: rgba(245, 158, 11, 0.1);
        }
        
        .file-input {
            display: none;
        }
        
        .upload-button {
            background: #f59e0b;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 16px;
        }
        
        .upload-button:hover {
            background: #d97706;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            background: #2a2a2a;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🔧 Upload Functionality Test</h1>
    <p>This tests the basic file upload functionality to ensure clicking and drag-drop work.</p>
    
    <div class="upload-area" id="uploadArea">
        <div>📁 Drop your file here or click to browse</div>
        <div style="font-size: 0.9rem; color: #999; margin-top: 8px;">Supports all file types</div>
        <input type="file" id="fileInput" class="file-input" accept="*/*">
        <button type="button" class="upload-button" id="uploadButton">
            Choose File
        </button>
    </div>
    
    <div class="status" id="status">
        Status: Ready for upload
    </div>
    
    <div style="margin-top: 20px;">
        <button onclick="testClick()" style="background: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 4px;">
            🧪 Test Click
        </button>
        <button onclick="checkElements()" style="background: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-left: 10px;">
            🔍 Check Elements
        </button>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').textContent = 'Status: ' + message;
            console.log('📊 Status:', message);
        }

        function testClick() {
            console.log('🧪 Testing programmatic click...');
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.click();
                updateStatus('Programmatic click triggered');
            } else {
                updateStatus('ERROR: File input not found');
            }
        }

        function checkElements() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const uploadButton = document.getElementById('uploadButton');
            
            console.log('🔍 Element check:', {
                fileInput: !!fileInput,
                uploadArea: !!uploadArea,
                uploadButton: !!uploadButton
            });
            
            updateStatus(`Elements found - Input: ${!!fileInput}, Area: ${!!uploadArea}, Button: ${!!uploadButton}`);
        }

        // Initialize when DOM loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 DOM loaded, setting up test...');
            
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const uploadButton = document.getElementById('uploadButton');
            
            if (!fileInput || !uploadArea || !uploadButton) {
                updateStatus('ERROR: Missing elements');
                return;
            }
            
            // File input change handler
            fileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                console.log('📁 Files selected:', files);
                if (files.length > 0) {
                    updateStatus(`File selected: ${files[0].name} (${files[0].size} bytes)`);
                } else {
                    updateStatus('No file selected');
                }
            });
            
            // Upload area click handler
            uploadArea.addEventListener('click', (e) => {
                console.log('🖱️ Upload area clicked');
                e.preventDefault();
                e.stopPropagation();
                fileInput.click();
                updateStatus('Upload area clicked - file dialog should open');
            });
            
            // Button click handler
            uploadButton.addEventListener('click', (e) => {
                console.log('🔘 Upload button clicked');
                e.preventDefault();
                e.stopPropagation();
                fileInput.click();
                updateStatus('Upload button clicked - file dialog should open');
            });
            
            // Drag and drop handlers
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = '#f59e0b';
                updateStatus('File being dragged over area');
            });
            
            uploadArea.addEventListener('dragleave', (e) => {
                uploadArea.style.borderColor = '#666';
                updateStatus('File drag left area');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = '#666';
                
                const files = e.dataTransfer.files;
                console.log('📂 Files dropped:', files);
                if (files.length > 0) {
                    updateStatus(`File dropped: ${files[0].name} (${files[0].size} bytes)`);
                } else {
                    updateStatus('No files in drop');
                }
            });
            
            updateStatus('Test initialized - try clicking or dropping files');
            console.log('✅ Test setup complete');
        });
    </script>
</body>
</html>