#!/usr/bin/env python3
"""
Test the DKK mislabeling fix.

This tests the specific scenario where:
- User has a DKK portfolio
- Interface shows "GAK 161.61 USD" but it's actually DKK
- System should detect this confusion and allow user to correct it
- When corrected, amounts stay the same but currency changes to DKK
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import AIPortfolioExtractor

def test_dkk_mislabeling_detection():
    """Test detection of DKK amounts mislabeled as USD."""
    print("🧪 Testing DKK Mislabeling Detection")
    print("=" * 60)
    
    # Simulate Danish portfolio text with USD labels (the actual user scenario)
    danish_portfolio_text = """
    GOOGL - Alphabet Inc
    13 stk
    GAK 161.61 USD
    Markedsværdi 2.462,85 kr
    Afkast +17.24%
    """
    
    # Create extractor with Danish language detection
    extractor = AIPortfolioExtractor("fake_api_key")
    extractor.detected_language = 'da'  # Force Danish detection
    extractor.detected_currency = 'DKK'  # Should detect DKK as primary
    
    print(f"📊 Input Text Analysis:")
    print(f"   Language: {extractor.detected_language}")
    print(f"   Primary Currency: {extractor.detected_currency}")
    print(f"   Text: {danish_portfolio_text.strip()}")
    
    # Simulate what Gemini would extract (USD labels but DKK amounts)
    gemini_response = [{
        "ticker": "GOOGL",
        "shares": 13.0,
        "buy_price": 161.61,
        "buy_price_currency": "USD",  # This is the mislabeling!
        "current_value": 2462.85,
        "current_value_currency": "DKK",
        "currency_uncertainty": "Danish interface with USD labels - amounts may actually be DKK"
    }]
    
    # Process the response
    processed_entries = extractor._process_gemini_response(gemini_response)
    
    print(f"\n🔍 Processing Results:")
    print(f"   Mixed currency detected: {getattr(extractor, 'mixed_currency_detected', False)}")
    print(f"   Currency confusion detected: {getattr(extractor, 'currency_confusion_detected', False)}")
    print(f"   Detected currencies: {getattr(extractor, 'detected_currencies', [])}")
    print(f"   Selection reason: {getattr(extractor, 'currency_selection_reason', 'None')}")
    
    # Verify confusion detection
    if (hasattr(extractor, 'currency_confusion_detected') and 
        extractor.currency_confusion_detected and
        'DKK' in getattr(extractor, 'detected_currencies', [])):
        print(f"✅ CURRENCY CONFUSION DETECTED CORRECTLY")
        print(f"   System identified potential DKK mislabeling")
        return True
    else:
        print(f"❌ CURRENCY CONFUSION NOT DETECTED")
        return False

def test_currency_correction_logic():
    """Test that currency correction preserves amounts but changes currency."""
    print("\n🧪 Testing Currency Correction Logic")
    print("=" * 60)
    
    # Simulate the correction scenario
    original_entry = {
        'ticker': 'GOOGL',
        'shares': 13.0,
        'amount_invested': 2100.93,  # This should stay the same
        'buy_price': 161.61,         # This should stay the same
        'buy_price_currency': 'USD', # This was the mislabeling
        'currency': 'USD'
    }
    
    # User selects DKK as the correct currency
    selected_currency = 'DKK'
    currency_confusion_detected = True
    
    print(f"📊 Original Entry:")
    print(f"   Amount Invested: {original_entry['amount_invested']} {original_entry['currency']}")
    print(f"   Buy Price: {original_entry['buy_price']} {original_entry['buy_price_currency']}")
    print(f"   Shares: {original_entry['shares']}")
    
    print(f"\n🔧 User Correction:")
    print(f"   User selected currency: {selected_currency}")
    print(f"   Currency confusion detected: {currency_confusion_detected}")
    
    # Apply correction logic (same as in app.py)
    if selected_currency != original_entry['buy_price_currency'] and currency_confusion_detected:
        # Currency correction - amounts stay same, currency changes
        corrected_entry = {
            'ticker': original_entry['ticker'],
            'shares': original_entry['shares'],
            'amount_invested': original_entry['amount_invested'],  # SAME AMOUNT
            'buy_price': original_entry['buy_price'],             # SAME PRICE
            'currency': selected_currency,                        # CORRECTED CURRENCY
            'buy_price_currency': selected_currency,
            'notes': f"[Currency corrected from {original_entry['currency']} to {selected_currency}]"
        }
        
        print(f"\n✅ CORRECTED ENTRY:")
        print(f"   Amount Invested: {corrected_entry['amount_invested']} {corrected_entry['currency']}")
        print(f"   Buy Price: {corrected_entry['buy_price']} {corrected_entry['buy_price_currency']}")
        print(f"   Shares: {corrected_entry['shares']}")
        print(f"   Notes: {corrected_entry['notes']}")
        
        # Verify the correction
        amounts_preserved = (
            corrected_entry['amount_invested'] == original_entry['amount_invested'] and
            corrected_entry['buy_price'] == original_entry['buy_price'] and
            corrected_entry['shares'] == original_entry['shares']
        )
        
        currency_corrected = (
            corrected_entry['currency'] == selected_currency and
            corrected_entry['buy_price_currency'] == selected_currency
        )
        
        if amounts_preserved and currency_corrected:
            print(f"✅ CURRENCY CORRECTION SUCCESSFUL")
            print(f"   ✓ Amounts preserved: {amounts_preserved}")
            print(f"   ✓ Currency corrected: {currency_corrected}")
            return True
        else:
            print(f"❌ CURRENCY CORRECTION FAILED")
            return False
    else:
        print(f"❌ CORRECTION LOGIC NOT TRIGGERED")
        return False

def test_share_calculation_verification():
    """Test that share calculations are correct after currency correction."""
    print("\n🧪 Testing Share Calculation After Currency Correction")
    print("=" * 60)
    
    # Test the exact user scenario: 13 shares × 161.61 DKK = 2100.93 DKK
    shares = 13.0
    buy_price_dkk = 161.61
    expected_amount_dkk = shares * buy_price_dkk
    
    print(f"📊 DKK Calculation Test:")
    print(f"   Shares: {shares}")
    print(f"   Buy Price: {buy_price_dkk} DKK")
    print(f"   Expected Amount: {shares} × {buy_price_dkk} = {expected_amount_dkk} DKK")
    
    # Verify calculation
    calculated_amount = shares * buy_price_dkk
    
    if abs(calculated_amount - expected_amount_dkk) < 0.01:
        print(f"✅ SHARE CALCULATION CORRECT")
        print(f"   Calculated: {calculated_amount} DKK")
        print(f"   Expected: {expected_amount_dkk} DKK")
        print(f"   Difference: {abs(calculated_amount - expected_amount_dkk):.4f} DKK")
        return True
    else:
        print(f"❌ SHARE CALCULATION ERROR")
        print(f"   Calculated: {calculated_amount} DKK")
        print(f"   Expected: {expected_amount_dkk} DKK")
        return False

def main():
    """Run all DKK mislabeling fix tests."""
    print("🚀 DKK MISLABELING FIX VERIFICATION")
    print("Testing the fix for DKK amounts mislabeled as USD")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Currency confusion detection
    if test_dkk_mislabeling_detection():
        tests_passed += 1
    
    # Test 2: Currency correction logic
    if test_currency_correction_logic():
        tests_passed += 1
    
    # Test 3: Share calculation verification
    if test_share_calculation_verification():
        tests_passed += 1
    
    print(f"\n🎯 TEST RESULTS:")
    print(f"   Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print(f"🎉 ALL TESTS PASSED!")
        print(f"✅ DKK mislabeling detection works correctly")
        print(f"✅ Currency correction preserves amounts")
        print(f"✅ Share calculations are accurate in DKK")
        print(f"\n💡 USER EXPERIENCE:")
        print(f"   1. System detects Danish text with USD labels")
        print(f"   2. Shows currency selection modal with clear explanation")
        print(f"   3. When user selects DKK, amounts stay the same")
        print(f"   4. Currency labels are corrected to DKK")
        print(f"   5. Share calculations remain accurate")
    else:
        print(f"⚠️  Some tests failed. DKK mislabeling fix needs work.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)